const multer = require('multer');
const cloudinary = require('../config/cloudinary');
const { Readable } = require('stream');

// Configurar multer para armazenar arquivos em memória
const storage = multer.memoryStorage();

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas imagens e PDFs
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não permitido. Apenas JPEG, PNG e PDF são aceitos.'), false);
    }
  }
});

// Função para fazer upload para o Cloudinary
const uploadToCloudinary = async (buffer, originalname, mimetype) => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        resource_type: mimetype.startsWith('image/') ? 'image' : 'raw',
        folder: 'transaction-receipts',
        public_id: `receipt_${Date.now()}_${originalname.split('.')[0]}`,
        format: mimetype === 'application/pdf' ? 'pdf' : undefined
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      }
    );

    // Converter buffer para stream e fazer upload
    const stream = Readable.from(buffer);
    stream.pipe(uploadStream);
  });
};

module.exports = {
  upload,
  uploadToCloudinary
};
