@echo off
echo ========================================
echo    🔍 Debug do Sistema de Empréstimos
echo ========================================
echo.
echo Verificando problemas específicos...
echo.

echo 1. ✅ Header dinâmico implementado
echo    • Título muda baseado na rota atual
echo    • /loans mostra "Empréstimos e Dívidas"
echo.

echo 2. ✅ Estatísticas do modal atualizadas
echo    • Cálculo em tempo real de pagamentos
echo    • Taxa de pontualidade calculada
echo    • Progresso atualizado automaticamente
echo.

echo 3. 🔍 Logs de debug adicionados no backend
echo    • Console.log nas criações de transação
echo    • Verificação de dados enviados
echo.

echo 4. 📋 Para testar as correções:
echo.
echo    A. TESTE DO HEADER:
echo       • Acesse /loans
echo       • Verifique se o título é "Empréstimos e Dívidas"
echo       • Navegue para outras páginas e veja se muda
echo.
echo    B. TESTE DAS ESTATÍSTICAS:
echo       • Crie um empréstimo
echo       • Pague algumas parcelas
echo       • Veja se as estatísticas atualizam
echo.
echo    C. TESTE DAS TRANSAÇÕES:
echo       • Abra o console do navegador (F12)
echo       • Crie um empréstimo
echo       • Veja se aparece na aba Transações
echo       • Verifique logs no terminal do backend
echo.
echo 5. 🚀 Iniciando sistema para debug...
echo.

timeout /t 3 /nobreak >nul
start http://localhost:5173/loans

echo ✅ Sistema iniciado!
echo.
echo 📝 Checklist de teste:
echo    □ Header mostra "Empréstimos e Dívidas"
echo    □ Estatísticas calculam corretamente
echo    □ Transações aparecem na aba Transações
echo    □ Progresso atualiza após pagamentos
echo    □ Botões de pagamento funcionam
echo.
echo 🔍 Se as transações não aparecerem:
echo    1. Verifique o console do navegador (F12)
echo    2. Verifique o terminal do backend
echo    3. Vá em Transações e recarregue a página
echo    4. Verifique se o banco foi selecionado
echo.
pause
