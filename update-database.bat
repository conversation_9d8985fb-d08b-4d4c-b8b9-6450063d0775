@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Atualização final - Cores automáticas por categoria:
echo   ✅ Cores automáticas para cada categoria nos gráficos
echo   ✅ Paleta de 20 cores vibrantes para diferenciação
echo   ✅ Gráfico de pizza com legenda compacta e alinhada
echo   ✅ Gráfico de linha com cores e padrões únicos por categoria
echo   ✅ Remoção de seleção de cor para cards de linha e pizza
echo   ✅ Aviso informativo sobre cores automáticas
echo   ✅ Layout otimizado do gráfico de pizza
echo   ✅ Percentuais apenas para fatias maiores que 5%
echo   ✅ Legenda centralizada e responsiva
echo   ✅ Sistema completo de diferenciação visual
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
