@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Última atualização - Cores vibrantes:
echo   ✅ Paleta de cores colorida e vibrante
echo   ✅ Cores organizadas por família (vermelho, verde, azul, etc)
echo   ✅ Seletor de cor customizada com preview
echo   ✅ Input de cor visual e campo de texto hex
echo   ✅ Botão toggle para alternar entre predefinida/customizada
echo   ✅ Grid organizado 8x4 para melhor visualização
echo   ✅ Hover effects e animações suaves
echo   ✅ Cores aplicadas em ambos os modais (criar/editar)
echo   ✅ Interface moderna e intuitiva
echo   ✅ Sistema de cores profissional e completo
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
