@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Funcionalidades finais implementadas:
echo   ✅ Modal de configuração de cards funcional
echo   ✅ Informações de categorias e período nos cards
echo   ✅ Gráfico de linha com múltiplas categorias
echo   ✅ Linhas diferenciadas por categoria (cores/tracejado)
echo   ✅ Legenda visual para múltiplas categorias
echo   ✅ Edição completa de cards (título, cor, período)
echo   ✅ Seleção de categorias no modal de edição
echo   ✅ Backend otimizado para múltiplas categorias
echo   ✅ Interface informativa e profissional
echo   ✅ Dashboard 100% funcional e completo
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
