@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Correções e melhorias implementadas:
echo   ✅ Filtros de data corrigidos nos cards
echo   ✅ Movimentação dos cards restaurada
echo   ✅ Persistência de perfil ativo implementada
echo   ✅ Tema escuro masculino (slate/gray)
echo   ✅ Cards respeitam ano e mês selecionados
echo   ✅ LocalStorage para perfil ativo
echo   ✅ Cores mais profissionais e masculinas
echo   ✅ Interface consistente em tons escuros
echo   ✅ Movimentação fluida pelo grip handle
echo   ✅ Sistema de perfis persistente
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
