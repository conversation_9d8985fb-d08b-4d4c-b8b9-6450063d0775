@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Melhorias implementadas:
echo   ✅ Movimentação aprimorada - apenas pelo canto superior
echo   ✅ Gráfico de linha com valores completos
echo   ✅ Tabela com scroll e ordenação por colunas
echo   ✅ Card numérico com comparação de períodos
echo   ✅ Tema roxo aplicado em toda interface
echo   ✅ Movimentação fluida sem travamentos
echo   ✅ Coluna de categoria na tabela
echo   ✅ Filtros de ordenação (data, valor, categoria)
echo   ✅ Comparação automática com período anterior
echo   ✅ Interface profissional com gradientes roxos
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
