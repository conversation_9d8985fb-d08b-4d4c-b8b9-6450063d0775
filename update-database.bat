@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Novas funcionalidades adicionadas:
echo   ✅ Dashboard Analytics totalmente customizável
echo   ✅ Cards drag & drop com posicionamento livre
echo   ✅ Cards numéricos, pizza, tabela e gráficos
echo   ✅ Modal de transações detalhadas
echo   ✅ Salvamento automático de posições
echo   ✅ Sistema de categorias conectáveis
echo   ✅ Gráfico de pizza na página inicial
echo   ✅ Sistema de perfis de dashboard
echo   ✅ Gráficos melhorados com valores e ordenação
echo   ✅ Seleção múltipla de categorias
echo   ✅ Área expandida do dashboard
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
