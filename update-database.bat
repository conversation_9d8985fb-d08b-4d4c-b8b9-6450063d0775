@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Sistema de Cofrinhos e Melhorias Implementadas:
echo.
echo 🏦 BANCOS E PAGAMENTOS:
echo   ✅ Banco obrigatório para formas de pagamento
echo   ✅ Validação corrigida no backend e frontend
echo   ✅ Interface atualizada com campo obrigatório
echo   ✅ Erro de include do PaymentMethod corrigido
echo.
echo 🐷 SISTEMA DE COFRINHOS:
echo   ✅ Modelo Savings completo no banco de dados
echo   ✅ APIs para CRUD de cofrinhos
echo   ✅ Sistema de depósito e saque
echo   ✅ Bloqueio/desbloqueio de cofrinhos
echo   ✅ Metas financeiras com progresso visual
echo   ✅ Transferência automática entre banco e cofrinho
echo   ✅ Interface moderna com cards informativos
echo.
echo 💰 FUNCIONALIDADES DOS COFRINHOS:
echo   ✅ Criação com nome, meta e banco vinculado
echo   ✅ Ícones e cores personalizáveis
echo   ✅ Barra de progresso para metas
echo   ✅ Depósito direto do saldo do banco
echo   ✅ Saque com validação de bloqueio
echo   ✅ Exclusão com devolução automática do saldo
echo   ✅ Terceira aba na página de bancos
echo.
echo 🎨 MELHORIAS DE INTERFACE:
echo   ✅ Componente CurrencyInput com máscara brasileira
echo   ✅ Formatação automática R$ 1.234,56
echo   ✅ Validações visuais em tempo real
echo   ✅ Design consistente com tema do sistema
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
