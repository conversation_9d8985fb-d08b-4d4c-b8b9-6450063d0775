{"name": "sara-backend", "version": "1.0.0", "description": "Backend API para sistema de gastos", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node src/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}}