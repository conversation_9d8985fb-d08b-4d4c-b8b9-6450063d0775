@echo off
echo ========================================
echo    Atualizando Banco - Empréstimos
echo ========================================
echo.
echo Aplicando mudanças no banco de dados...
cd backend
echo.
echo 1. Gerando cliente Prisma...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)
echo.
echo 2. Aplicando mudanças no banco...
call npx prisma db push --accept-data-loss
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)
echo.
echo ✅ Banco atualizado com sucesso!
echo.
echo 📋 Novos recursos adicionados:
echo    • Contatos para empréstimos
echo    • Sistema de empréstimos e dívidas
echo    • Controle de parcelas
echo    • Status de pagadores
echo    • Integração com bancos
echo.
echo 🚀 Agora você pode usar: start.bat
echo.
pause
