@echo off
echo ========================================
echo 🏦 MIGRANDO SISTEMA DE BANCOS E PAGAMENTOS
echo ========================================
echo.

cd backend

echo 📊 Gerando migração do Prisma...
npx prisma db push

echo.
echo 🔄 Atualizando cliente Prisma...
npx prisma generate

echo.
echo 🏦 Configurando bancos e formas de pagamento padrão...
node src/scripts/setupBanksAndPayments.js

echo.
echo ========================================
echo ✅ Migração concluída com sucesso!
echo ========================================
echo.
echo 🆕 Sistema completo de bancos e pagamentos implementado:
echo.
echo 🏦 BACKEND:
echo   ✅ Modelos Bank e PaymentMethod no Prisma
echo   ✅ APIs completas para CRUD de bancos
echo   ✅ APIs completas para CRUD de formas de pagamento
echo   ✅ Sistema de transferências entre bancos
echo   ✅ Controle automático de saldos
echo   ✅ Transações vinculadas a bancos e pagamentos
echo   ✅ Validações e segurança implementadas
echo.
echo 🎨 FRONTEND:
echo   ✅ Página completa de gestão de bancos
echo   ✅ Interface para formas de pagamento
echo   ✅ Modais de criação e edição
echo   ✅ Controle de visibilidade de saldos
echo   ✅ Design responsivo e moderno
echo   ✅ Integração com sidebar e rotas
echo   ✅ Feedback visual e notificações
echo.
echo 💰 FUNCIONALIDADES:
echo   ✅ Criação de bancos com saldo inicial
echo   ✅ Edição de ícones e cores
echo   ✅ Ocultação/exibição de saldos
echo   ✅ Transferências entre contas
echo   ✅ Formas de pagamento padrão (PIX, cartão, etc)
echo   ✅ Personalização completa
echo   ✅ Dados padrão criados automaticamente
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
pause
