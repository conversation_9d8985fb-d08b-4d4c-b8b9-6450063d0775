import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  CreditCard,
  Tag,
  PieChart,
  Settings,
  LogOut,
  Moon,
  BarChart3,
  Wallet,
  HandHeart
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

function Sidebar({ isOpen, onClose }) {
  const location = useLocation()
  const { logout } = useAuth()

  const menuItems = [
    { path: '/dashboard', icon: Home, label: 'Página Inicial' },
    { path: '/dashboard-analytics', icon: BarChart3, label: 'Dashboard' },
    { path: '/transactions', icon: CreditCard, label: 'Transações' },
    { path: '/categories', icon: Tag, label: 'Categorias' },
    { path: '/banks', icon: Wallet, label: 'Bancos' },
    { path: '/loans', icon: HandHeart, label: 'Empréstimos' },
    { path: '/reports', icon: <PERSON><PERSON><PERSON>, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { path: '/settings', icon: Settings, label: 'Configurações' },
  ]

  const handleLogout = () => {
    logout()
    onClose()
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-sidebar-bg overflow-y-auto">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 py-6">
              <div className="flex items-center">
                <Moon className="h-8 w-8 text-purple-400" />
                <span className="ml-3 text-xl font-bold text-white">Luar</span>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-2 pb-4 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = location.pathname === item.path

                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`sidebar-item ${isActive ? 'active' : ''}`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.label}
                  </Link>
                )
              })}
            </nav>

            {/* Logout */}
            <div className="flex-shrink-0 px-2 pb-4">
              <button
                onClick={handleLogout}
                className="sidebar-item w-full text-left"
              >
                <LogOut className="mr-3 h-5 w-5" />
                Sair
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-sidebar-bg transform ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:hidden`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4 py-6">
            <div className="flex items-center">
              <Moon className="h-8 w-8 text-purple-400" />
              <span className="ml-3 text-xl font-bold text-white">Luar</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 pb-4 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.path

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={onClose}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.label}
                </Link>
              )
            })}
          </nav>

          {/* Logout */}
          <div className="flex-shrink-0 px-2 pb-4">
            <button
              onClick={handleLogout}
              className="sidebar-item w-full text-left"
            >
              <LogOut className="mr-3 h-5 w-5" />
              Sair
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
