import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Filter, Search, Upload, Eye, Download, Calendar, DollarSign, Tag } from 'lucide-react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import api from '../services/api'
import { bankService, paymentMethodService } from '../services/bankService'
import CurrencyInput from '../components/CurrencyInput'

function Transactions() {
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState(null)
  const [transactionAmount, setTransactionAmount] = useState(0)
  const [selectedFile, setSelectedFile] = useState(null)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    type: '',
    categoryId: '',
    bankId: '',
    paymentMethodId: '',
    search: '',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: '',
    hasReceipt: ''
  })

  const { register, handleSubmit, reset, formState: { errors } } = useForm()

  useEffect(() => {
    fetchTransactions()
    fetchCategories()
    fetchBanks()
    fetchPaymentMethods()
  }, [filters])

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      console.error('Erro ao carregar bancos:', error)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      setPaymentMethods(data)
    } catch (error) {
      console.error('Erro ao carregar formas de pagamento:', error)
    }
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (filters.type) params.append('type', filters.type)
      if (filters.categoryId) params.append('categoryId', filters.categoryId)

      const response = await api.get(`/transactions?${params.toString()}`)
      setTransactions(response.data.transactions)
    } catch (error) {
      console.error('Erro ao buscar transações:', error)
      toast.error('Erro ao carregar transações')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const onSubmit = async (data) => {
    try {
      const formData = new FormData()

      // Adicionar dados da transação
      formData.append('description', data.description)
      formData.append('amount', transactionAmount)
      formData.append('type', data.type)
      formData.append('date', data.date)

      if (data.categoryId) formData.append('categoryId', data.categoryId)
      if (data.bankId) formData.append('bankId', data.bankId)
      if (data.paymentMethodId) formData.append('paymentMethodId', data.paymentMethodId)

      // Adicionar arquivo se selecionado
      if (selectedFile) {
        formData.append('receipt', selectedFile)
      }

      if (editingTransaction) {
        // Para edição, usar JSON normal (sem arquivo por enquanto)
        const transactionData = {
          ...data,
          amount: transactionAmount,
          bankId: data.bankId || null,
          paymentMethodId: data.paymentMethodId || null
        }
        await api.put(`/transactions/${editingTransaction.id}`, transactionData)
        toast.success('Transação atualizada com sucesso!')
      } else {
        await api.post('/transactions', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        toast.success('Transação criada com sucesso!')
      }

      setShowModal(false)
      setEditingTransaction(null)
      setTransactionAmount(0)
      setSelectedFile(null)
      reset()
      fetchTransactions()
      fetchBanks() // Atualizar saldos dos bancos
    } catch (error) {
      console.error('Erro ao salvar transação:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    }
  }

  const handleEdit = (transaction) => {
    setEditingTransaction(transaction)
    setTransactionAmount(transaction.amount)
    reset({
      description: transaction.description,
      type: transaction.type,
      categoryId: transaction.categoryId || '',
      bankId: transaction.bankId || '',
      paymentMethodId: transaction.paymentMethodId || '',
      date: transaction.date.split('T')[0]
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja deletar esta transação?')) {
      try {
        await api.delete(`/transactions/${id}`)
        toast.success('Transação deletada com sucesso!')
        fetchTransactions()
      } catch (error) {
        console.error('Erro ao deletar transação:', error)
        toast.error('Erro ao deletar transação')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'INCOME': return 'text-green-600 bg-green-100'
      case 'EXPENSE': return 'text-red-600 bg-red-100'
      case 'INVESTMENT': return 'text-blue-600 bg-blue-100'
      case 'LOAN': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'INCOME': return 'Receita'
      case 'EXPENSE': return 'Despesa'
      case 'INVESTMENT': return 'Investimento'
      case 'LOAN': return 'Empréstimo'
      default: return type
    }
  }

  const filteredTransactions = transactions.filter(transaction => {
    if (filters.search && !transaction.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    return true
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Transações</h1>
          <p className="text-gray-600 mt-1">Gerencie suas receitas e despesas com controle total</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              showFilters
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4" />
            <span>Filtros</span>
          </button>
          <button
            onClick={() => {
              setEditingTransaction(null)
              setTransactionAmount(0)
              setSelectedFile(null)
              reset()
              setShowModal(true)
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Transação</span>
          </button>
        </div>
      </div>

      {/* Filtros Avançados */}
      {showFilters && (
        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Filtros Avançados</h3>
            <button
              onClick={() => setFilters({
                type: '', categoryId: '', bankId: '', paymentMethodId: '', search: '',
                dateFrom: '', dateTo: '', amountMin: '', amountMax: '', hasReceipt: ''
              })}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Limpar Todos
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* Busca */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search className="inline h-4 w-4 mr-1" />
                Buscar
              </label>
              <input
                type="text"
                placeholder="Descrição da transação..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Tipo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="inline h-4 w-4 mr-1" />
                Tipo
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              >
                <option value="">Todos os tipos</option>
                <option value="INCOME">💰 Receita</option>
                <option value="EXPENSE">💸 Despesa</option>
                <option value="INVESTMENT">📈 Investimento</option>
                <option value="LOAN">🏦 Empréstimo</option>
              </select>
            </div>

            {/* Categoria */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.categoryId}
                onChange={(e) => setFilters({ ...filters, categoryId: e.target.value })}
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Banco */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Banco</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.bankId}
                onChange={(e) => setFilters({ ...filters, bankId: e.target.value })}
              >
                <option value="">Todos os bancos</option>
                {banks.map((bank) => (
                  <option key={bank.id} value={bank.id}>
                    {bank.icon} {bank.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Data De */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data De
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.dateFrom}
                onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              />
            </div>

            {/* Data Até */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data Até
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.dateTo}
                onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              />
            </div>

            {/* Valor Mínimo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Mínimo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.amountMin}
                onChange={(e) => setFilters({ ...filters, amountMin: e.target.value })}
              />
            </div>

            {/* Valor Máximo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Máximo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.amountMax}
                onChange={(e) => setFilters({ ...filters, amountMax: e.target.value })}
              />
            </div>
          </div>
        </div>
      )}

      {/* Lista de Transações */}
      <div className="bg-white rounded-2xl border shadow-sm overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <Search className="h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma transação encontrada</h3>
            <p className="text-gray-600">Tente ajustar os filtros ou criar uma nova transação</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transação
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Categoria
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Banco/Pagamento
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Comprovante
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3 ${
                          transaction.type === 'INCOME' ? 'bg-green-500' :
                          transaction.type === 'EXPENSE' ? 'bg-red-500' :
                          transaction.type === 'INVESTMENT' ? 'bg-blue-500' : 'bg-gray-500'
                        }`}>
                          {transaction.type === 'INCOME' ? '↗' :
                           transaction.type === 'EXPENSE' ? '↙' :
                           transaction.type === 'INVESTMENT' ? '📈' : '🏦'}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {transaction.description}
                          </div>
                          <div className="text-xs text-gray-500">
                            {getTypeLabel(transaction.type)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {transaction.category ? (
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{transaction.category.icon}</span>
                          <span className="text-sm text-gray-900">{transaction.category.name}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">Sem categoria</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {transaction.bank && (
                          <div className="flex items-center text-gray-900 mb-1">
                            <span className="mr-1">{transaction.bank.icon}</span>
                            {transaction.bank.name}
                          </div>
                        )}
                        {transaction.paymentMethod && (
                          <div className="flex items-center text-gray-600 text-xs">
                            <span className="mr-1">{transaction.paymentMethod.icon}</span>
                            {transaction.paymentMethod.name}
                          </div>
                        )}
                        {!transaction.bank && !transaction.paymentMethod && (
                          <span className="text-gray-400 text-sm">-</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className={`text-sm font-semibold ${
                        transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'INCOME' ? '+' : '-'}
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(transaction.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {new Date(transaction.date).toLocaleDateString('pt-BR')}
                    </td>
                    <td className="px-6 py-4">
                      {transaction.receiptUrl ? (
                        <button
                          onClick={() => window.open(transaction.receiptUrl, '_blank')}
                          className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                        >
                          <Eye className="h-3 w-3" />
                          Ver
                        </button>
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(transaction)}
                          className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                          title="Editar"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(transaction.id)}
                          className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                          title="Excluir"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {editingTransaction ? 'Editar Transação' : 'Nova Transação'}
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <input
                  {...register('description', { required: 'Descrição é obrigatória' })}
                  type="text"
                  className="input-field"
                  placeholder="Descrição da transação"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor
                </label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="input-field"
                  placeholder="R$ 0,00"
                />
                {transactionAmount <= 0 && (
                  <p className="mt-1 text-sm text-red-600">Valor deve ser maior que zero</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  {...register('type', { required: 'Tipo é obrigatório' })}
                  className="input-field"
                >
                  <option value="">Selecione o tipo</option>
                  <option value="INCOME">Receita</option>
                  <option value="EXPENSE">Despesa</option>
                  <option value="INVESTMENT">Investimento</option>
                  <option value="LOAN">Empréstimo</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  {...register('categoryId')}
                  className="input-field"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banco
                </label>
                <select
                  {...register('bankId')}
                  className="input-field"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name} - {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(bank.currentBalance)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Forma de Pagamento
                </label>
                <select
                  {...register('paymentMethodId')}
                  className="input-field"
                >
                  <option value="">Selecione uma forma de pagamento</option>
                  {paymentMethods.map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.icon} {method.name}
                      {method.bank && ` (${method.bank.name})`}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data
                </label>
                <input
                  {...register('date')}
                  type="date"
                  className="input-field"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>

              {/* Upload de Comprovante */}
              {!editingTransaction && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Upload className="inline h-4 w-4 mr-1" />
                    Comprovante (Opcional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => setSelectedFile(e.target.files[0])}
                      className="hidden"
                      id="receipt-upload"
                    />
                    <label htmlFor="receipt-upload" className="cursor-pointer">
                      {selectedFile ? (
                        <div className="flex items-center justify-center gap-2 text-green-600">
                          <Upload className="h-5 w-5" />
                          <span className="text-sm font-medium">{selectedFile.name}</span>
                        </div>
                      ) : (
                        <div className="text-gray-500">
                          <Upload className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-sm">Clique para enviar uma foto ou PDF</p>
                          <p className="text-xs text-gray-400 mt-1">PNG, JPG ou PDF até 10MB</p>
                        </div>
                      )}
                    </label>
                  </div>
                  {selectedFile && (
                    <button
                      type="button"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2 text-sm text-red-600 hover:text-red-800"
                    >
                      Remover arquivo
                    </button>
                  )}
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false)
                    setEditingTransaction(null)
                    setTransactionAmount(0)
                    reset()
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                >
                  {editingTransaction ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Transactions
