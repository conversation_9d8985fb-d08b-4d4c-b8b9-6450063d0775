import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Filter, Search } from 'lucide-react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import api from '../services/api'
import { bankService, paymentMethodService } from '../services/bankService'
import CurrencyInput from '../components/CurrencyInput'

function Transactions() {
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState(null)
  const [transactionAmount, setTransactionAmount] = useState(0)
  const [filters, setFilters] = useState({
    type: '',
    categoryId: '',
    search: ''
  })

  const { register, handleSubmit, reset, formState: { errors } } = useForm()

  useEffect(() => {
    fetchTransactions()
    fetchCategories()
    fetchBanks()
    fetchPaymentMethods()
  }, [filters])

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      console.error('Erro ao carregar bancos:', error)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      setPaymentMethods(data)
    } catch (error) {
      console.error('Erro ao carregar formas de pagamento:', error)
    }
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (filters.type) params.append('type', filters.type)
      if (filters.categoryId) params.append('categoryId', filters.categoryId)

      const response = await api.get(`/transactions?${params.toString()}`)
      setTransactions(response.data.transactions)
    } catch (error) {
      console.error('Erro ao buscar transações:', error)
      toast.error('Erro ao carregar transações')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const onSubmit = async (data) => {
    try {
      const transactionData = {
        ...data,
        amount: transactionAmount,
        bankId: data.bankId || null,
        paymentMethodId: data.paymentMethodId || null
      }

      if (editingTransaction) {
        await api.put(`/transactions/${editingTransaction.id}`, transactionData)
        toast.success('Transação atualizada com sucesso!')
      } else {
        await api.post('/transactions', transactionData)
        toast.success('Transação criada com sucesso!')
      }

      setShowModal(false)
      setEditingTransaction(null)
      setTransactionAmount(0)
      reset()
      fetchTransactions()
      fetchBanks() // Atualizar saldos dos bancos
    } catch (error) {
      console.error('Erro ao salvar transação:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    }
  }

  const handleEdit = (transaction) => {
    setEditingTransaction(transaction)
    setTransactionAmount(transaction.amount)
    reset({
      description: transaction.description,
      type: transaction.type,
      categoryId: transaction.categoryId || '',
      bankId: transaction.bankId || '',
      paymentMethodId: transaction.paymentMethodId || '',
      date: transaction.date.split('T')[0]
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja deletar esta transação?')) {
      try {
        await api.delete(`/transactions/${id}`)
        toast.success('Transação deletada com sucesso!')
        fetchTransactions()
      } catch (error) {
        console.error('Erro ao deletar transação:', error)
        toast.error('Erro ao deletar transação')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'INCOME': return 'text-green-600 bg-green-100'
      case 'EXPENSE': return 'text-red-600 bg-red-100'
      case 'INVESTMENT': return 'text-blue-600 bg-blue-100'
      case 'LOAN': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'INCOME': return 'Receita'
      case 'EXPENSE': return 'Despesa'
      case 'INVESTMENT': return 'Investimento'
      case 'LOAN': return 'Empréstimo'
      default: return type
    }
  }

  const filteredTransactions = transactions.filter(transaction => {
    if (filters.search && !transaction.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    return true
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Transações</h1>
        <button
          onClick={() => {
            setEditingTransaction(null)
            setTransactionAmount(0)
            reset()
            setShowModal(true)
          }}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Nova Transação
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar transações..."
                className="input-field pl-10"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipo
            </label>
            <select
              className="input-field"
              value={filters.type}
              onChange={(e) => setFilters({ ...filters, type: e.target.value })}
            >
              <option value="">Todos os tipos</option>
              <option value="INCOME">Receita</option>
              <option value="EXPENSE">Despesa</option>
              <option value="INVESTMENT">Investimento</option>
              <option value="LOAN">Empréstimo</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoria
            </label>
            <select
              className="input-field"
              value={filters.categoryId}
              onChange={(e) => setFilters({ ...filters, categoryId: e.target.value })}
            >
              <option value="">Todas as categorias</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ type: '', categoryId: '', search: '' })}
              className="btn-secondary w-full"
            >
              Limpar Filtros
            </button>
          </div>
        </div>
      </div>

      {/* Transactions List */}
      <div className="card">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">Nenhuma transação encontrada</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Descrição
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Categoria
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {transaction.category ? (
                          <span className="flex items-center">
                            <span className="mr-2">{transaction.category.icon}</span>
                            {transaction.category.name}
                          </span>
                        ) : (
                          <span className="text-gray-400">Sem categoria</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(transaction.type)}`}>
                        {getTypeLabel(transaction.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${
                        transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'INCOME' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(transaction.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(transaction)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(transaction.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {editingTransaction ? 'Editar Transação' : 'Nova Transação'}
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <input
                  {...register('description', { required: 'Descrição é obrigatória' })}
                  type="text"
                  className="input-field"
                  placeholder="Descrição da transação"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor
                </label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="input-field"
                  placeholder="R$ 0,00"
                />
                {transactionAmount <= 0 && (
                  <p className="mt-1 text-sm text-red-600">Valor deve ser maior que zero</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  {...register('type', { required: 'Tipo é obrigatório' })}
                  className="input-field"
                >
                  <option value="">Selecione o tipo</option>
                  <option value="INCOME">Receita</option>
                  <option value="EXPENSE">Despesa</option>
                  <option value="INVESTMENT">Investimento</option>
                  <option value="LOAN">Empréstimo</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  {...register('categoryId')}
                  className="input-field"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banco
                </label>
                <select
                  {...register('bankId')}
                  className="input-field"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name} - {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(bank.currentBalance)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Forma de Pagamento
                </label>
                <select
                  {...register('paymentMethodId')}
                  className="input-field"
                >
                  <option value="">Selecione uma forma de pagamento</option>
                  {paymentMethods.map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.icon} {method.name}
                      {method.bank && ` (${method.bank.name})`}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data
                </label>
                <input
                  {...register('date')}
                  type="date"
                  className="input-field"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false)
                    setEditingTransaction(null)
                    setTransactionAmount(0)
                    reset()
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                >
                  {editingTransaction ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Transactions
