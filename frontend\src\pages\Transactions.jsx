import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Filter, Search, Upload, Eye, Download, Calendar, DollarSign, Tag, CreditCard } from 'lucide-react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import api from '../services/api'
import { bankService, paymentMethodService } from '../services/bankService'
import CurrencyInput from '../components/CurrencyInput'
import ReceiptModal from '../components/ReceiptModal'
import InstallmentModal from '../components/InstallmentModal'

function Transactions() {
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState(null)
  const [transactionAmount, setTransactionAmount] = useState(0)
  const [selectedFile, setSelectedFile] = useState(null)
  const [showFilters, setShowFilters] = useState(false)
  const [installments, setInstallments] = useState(1)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [showReceiptModal, setShowReceiptModal] = useState(false)
  const [selectedReceipt, setSelectedReceipt] = useState(null)
  const [showInstallmentModal, setShowInstallmentModal] = useState(false)
  const [selectedInstallment, setSelectedInstallment] = useState(null)
  const [installmentDetails, setInstallmentDetails] = useState([])
  const [filters, setFilters] = useState({
    type: '',
    categoryId: '',
    bankId: '',
    paymentMethodId: '',
    search: '',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: '',
    hasReceipt: ''
  })

  // Resetar página quando filtros mudarem
  useEffect(() => {
    setCurrentPage(1)
  }, [filters])

  const { register, handleSubmit, reset, formState: { errors } } = useForm()

  useEffect(() => {
    fetchTransactions()
    fetchCategories()
    fetchBanks()
    fetchPaymentMethods()
  }, [filters])

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      console.error('Erro ao carregar bancos:', error)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      setPaymentMethods(data)
    } catch (error) {
      console.error('Erro ao carregar formas de pagamento:', error)
    }
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await api.get('/transactions')

      setTransactions(response.data)
    } catch (error) {
      console.error('Erro ao buscar transações:', error)
      toast.error('Erro ao carregar transações')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const onSubmit = async (data) => {
    try {
      const formData = new FormData()

      // Adicionar dados da transação
      formData.append('description', data.description)
      formData.append('amount', transactionAmount)
      formData.append('type', data.type)
      formData.append('date', data.date)
      formData.append('installments', installments)

      if (data.categoryId) formData.append('categoryId', data.categoryId)
      if (data.bankId) formData.append('bankId', data.bankId)
      if (data.paymentMethodId) formData.append('paymentMethodId', data.paymentMethodId)

      // Adicionar arquivo se selecionado
      if (selectedFile) {
        formData.append('receipt', selectedFile)
      }

      if (editingTransaction) {
        // Para edição, usar JSON normal (sem arquivo por enquanto)
        const transactionData = {
          ...data,
          amount: transactionAmount,
          bankId: data.bankId || null,
          paymentMethodId: data.paymentMethodId || null
        }
        await api.put(`/transactions/${editingTransaction.id}`, transactionData)
        toast.success('Transação atualizada com sucesso!')
      } else {
        await api.post('/transactions', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        toast.success('Transação criada com sucesso!')
      }

      setShowModal(false)
      setEditingTransaction(null)
      setTransactionAmount(0)
      setSelectedFile(null)
      setInstallments(1)
      setSelectedPaymentMethod(null)
      reset()
      fetchTransactions()
      fetchBanks() // Atualizar saldos dos bancos
    } catch (error) {
      console.error('Erro ao salvar transação:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    }
  }

  const handleEdit = (transaction) => {
    setEditingTransaction(transaction)
    setTransactionAmount(transaction.amount)
    reset({
      description: transaction.description,
      type: transaction.type,
      categoryId: transaction.categoryId || '',
      bankId: transaction.bankId || '',
      paymentMethodId: transaction.paymentMethodId || '',
      date: transaction.date.split('T')[0]
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja deletar esta transação?')) {
      try {
        await api.delete(`/transactions/${id}`)
        toast.success('Transação deletada com sucesso!')
        fetchTransactions()
      } catch (error) {
        console.error('Erro ao deletar transação:', error)
        toast.error('Erro ao deletar transação')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const handleViewReceipt = (receiptUrl, description) => {
    setSelectedReceipt({ url: receiptUrl, description })
    setShowReceiptModal(true)
  }

  const handleViewInstallment = async (transaction) => {
    try {
      // Buscar detalhes das parcelas
      const response = await api.get(`/transactions/${transaction.id}/installments`)
      setSelectedInstallment(transaction)
      setInstallmentDetails(response.data)
      setShowInstallmentModal(true)
    } catch (error) {
      console.error('Erro ao buscar detalhes das parcelas:', error)
      toast.error('Erro ao carregar detalhes das parcelas')
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'INCOME': return 'text-green-600 bg-green-100'
      case 'EXPENSE': return 'text-red-600 bg-red-100'
      case 'INVESTMENT': return 'text-blue-600 bg-blue-100'
      case 'LOAN': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'INCOME': return 'Receita'
      case 'EXPENSE': return 'Despesa'
      case 'INVESTMENT': return 'Investimento'
      case 'LOAN': return 'Empréstimo'
      default: return type
    }
  }

  // Aplicar filtros
  const filteredTransactions = transactions.filter(transaction => {


    // Filtro por busca (descrição)
    if (filters.search && filters.search.trim() !== '') {
      const searchTerm = filters.search.toLowerCase().trim()
      if (!transaction.description?.toLowerCase().includes(searchTerm)) {
        return false
      }
    }

    // Filtro por tipo
    if (filters.type && filters.type !== '') {
      if (transaction.type !== filters.type) {
        return false
      }
    }

    // Filtro por categoria
    if (filters.categoryId && filters.categoryId !== '') {
      if (transaction.categoryId !== filters.categoryId) {
        return false
      }
    }

    // Filtro por banco
    if (filters.bankId && filters.bankId !== '') {
      if (transaction.bankId !== filters.bankId) {
        return false
      }
    }

    // Filtro por forma de pagamento
    if (filters.paymentMethodId && filters.paymentMethodId !== '') {
      if (transaction.paymentMethodId !== filters.paymentMethodId) {
        return false
      }
    }

    // Filtro por data de início
    if (filters.dateFrom && filters.dateFrom !== '') {
      const transactionDate = new Date(transaction.date)
      const fromDate = new Date(filters.dateFrom)
      fromDate.setHours(0, 0, 0, 0)
      transactionDate.setHours(0, 0, 0, 0)
      if (transactionDate < fromDate) {
        return false
      }
    }

    // Filtro por data final
    if (filters.dateTo && filters.dateTo !== '') {
      const transactionDate = new Date(transaction.date)
      const toDate = new Date(filters.dateTo)
      transactionDate.setHours(0, 0, 0, 0)
      toDate.setHours(23, 59, 59, 999)
      if (transactionDate > toDate) {
        return false
      }
    }

    // Filtro por valor mínimo
    if (filters.amountMin && filters.amountMin !== '') {
      const minAmount = parseFloat(filters.amountMin)
      if (!isNaN(minAmount) && transaction.amount < minAmount) {
        return false
      }
    }

    // Filtro por valor máximo
    if (filters.amountMax && filters.amountMax !== '') {
      const maxAmount = parseFloat(filters.amountMax)
      if (!isNaN(maxAmount) && transaction.amount > maxAmount) {
        return false
      }
    }

    // Filtro por comprovante
    if (filters.hasReceipt && filters.hasReceipt !== '') {
      if (filters.hasReceipt === 'true' && !transaction.receiptUrl) {
        return false
      }
      if (filters.hasReceipt === 'false' && transaction.receiptUrl) {
        return false
      }
    }

    return true
  })

  // Aplicar paginação
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Transações</h1>
          <p className="text-gray-600 mt-1">Gerencie suas receitas e despesas com controle total</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              showFilters
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4" />
            <span>Filtros</span>
          </button>
          <button
            onClick={() => {
              setEditingTransaction(null)
              setTransactionAmount(0)
              setSelectedFile(null)
              reset()
              setShowModal(true)
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Transação</span>
          </button>
        </div>
      </div>

      {/* Filtros Avançados */}
      {showFilters && (
        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Filtros Avançados</h3>
            <button
              onClick={() => setFilters({
                type: '', categoryId: '', bankId: '', paymentMethodId: '', search: '',
                dateFrom: '', dateTo: '', amountMin: '', amountMax: '', hasReceipt: ''
              })}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Limpar Todos
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* Busca */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search className="inline h-4 w-4 mr-1" />
                Buscar
              </label>
              <input
                type="text"
                placeholder="Descrição da transação..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>

            {/* Tipo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="inline h-4 w-4 mr-1" />
                Tipo
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              >
                <option value="">Todos os tipos</option>
                <option value="INCOME">💰 Receita</option>
                <option value="EXPENSE">💸 Despesa</option>
                <option value="INVESTMENT">📈 Investimento</option>
                <option value="LOAN">🏦 Empréstimo</option>
              </select>
            </div>

            {/* Categoria */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.categoryId}
                onChange={(e) => setFilters({ ...filters, categoryId: e.target.value })}
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Banco */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Banco</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.bankId}
                onChange={(e) => setFilters({ ...filters, bankId: e.target.value })}
              >
                <option value="">Todos os bancos</option>
                {banks.map((bank) => (
                  <option key={bank.id} value={bank.id}>
                    {bank.icon} {bank.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Data De */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data De
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.dateFrom}
                onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              />
            </div>

            {/* Data Até */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data Até
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.dateTo}
                onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              />
            </div>

            {/* Valor Mínimo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Mínimo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.amountMin}
                onChange={(e) => setFilters({ ...filters, amountMin: e.target.value })}
              />
            </div>

            {/* Valor Máximo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Máximo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.amountMax}
                onChange={(e) => setFilters({ ...filters, amountMax: e.target.value })}
              />
            </div>

            {/* Forma de Pagamento */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Forma de Pagamento</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.paymentMethodId}
                onChange={(e) => setFilters({ ...filters, paymentMethodId: e.target.value })}
              >
                <option value="">Todas as formas</option>
                {paymentMethods.map((method) => (
                  <option key={method.id} value={method.id}>
                    {method.icon} {method.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Comprovante */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Comprovante</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filters.hasReceipt}
                onChange={(e) => setFilters({ ...filters, hasReceipt: e.target.value })}
              >
                <option value="">Todos</option>
                <option value="true">Com comprovante</option>
                <option value="false">Sem comprovante</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Lista de Transações */}
      <div className="bg-white rounded-2xl border shadow-sm overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : paginatedTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <Search className="h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma transação encontrada</h3>
            <p className="text-gray-600">Tente ajustar os filtros ou criar uma nova transação</p>
          </div>
        ) : (
          <div className="overflow-x-auto max-h-96 overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transação
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Categoria
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Banco/Pagamento
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Comprovante
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3 ${
                          transaction.type === 'INCOME' ? 'bg-green-500' :
                          transaction.type === 'EXPENSE' ? 'bg-red-500' :
                          transaction.type === 'INVESTMENT' ? 'bg-blue-500' : 'bg-gray-500'
                        }`}>
                          {transaction.type === 'INCOME' ? '↗' :
                           transaction.type === 'EXPENSE' ? '↙' :
                           transaction.type === 'INVESTMENT' ? '📈' : '🏦'}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-medium text-gray-900">
                              {transaction.description}
                            </div>
                            {transaction.installments > 1 && (
                              <button
                                onClick={() => handleViewInstallment(transaction)}
                                className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                                title="Ver detalhes do parcelamento"
                              >
                                <CreditCard className="h-3 w-3" />
                                {transaction.installments}x
                              </button>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {getTypeLabel(transaction.type)}
                            {transaction.installments > 1 && (
                              <span className="ml-2 text-blue-600">
                                • Parcelado em {transaction.installments}x
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {transaction.category ? (
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{transaction.category.icon}</span>
                          <span className="text-sm text-gray-900">{transaction.category.name}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">Sem categoria</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {transaction.bank && (
                          <div className="flex items-center text-gray-900 mb-1">
                            <span className="mr-1">{transaction.bank.icon}</span>
                            {transaction.bank.name}
                          </div>
                        )}
                        {transaction.paymentMethod && (
                          <div className="flex items-center text-gray-600 text-xs">
                            <span className="mr-1">{transaction.paymentMethod.icon}</span>
                            {transaction.paymentMethod.name}
                          </div>
                        )}
                        {!transaction.bank && !transaction.paymentMethod && (
                          <span className="text-gray-400 text-sm">-</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className={`text-sm font-semibold ${
                        transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'INCOME' ? '+' : '-'}
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(transaction.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {new Date(transaction.date).toLocaleDateString('pt-BR')}
                    </td>
                    <td className="px-6 py-4">
                      {transaction.receiptUrl ? (
                        <div className="flex items-center gap-2">
                          {/* Thumbnail */}
                          <div className="w-8 h-8 rounded border overflow-hidden bg-gray-100">
                            {transaction.receiptUrl.includes('image') || transaction.receiptUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                              <img
                                src={transaction.receiptUrl}
                                alt="Comprovante"
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.target.style.display = 'none'
                                  e.target.nextSibling.style.display = 'flex'
                                }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-gray-400">
                                📄
                              </div>
                            )}
                            <div className="w-full h-full hidden items-center justify-center text-gray-400 text-xs">
                              📄
                            </div>
                          </div>
                          {/* Botão Ver */}
                          <button
                            onClick={() => handleViewReceipt(transaction.receiptUrl, transaction.description)}
                            className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                            title="Visualizar comprovante"
                          >
                            <Eye className="h-3 w-3" />
                            Ver
                          </button>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(transaction)}
                          className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                          title="Editar"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(transaction.id)}
                          className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                          title="Excluir"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Paginação */}
        {filteredTransactions.length > itemsPerPage && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Mostrando {startIndex + 1} a {Math.min(endIndex, filteredTransactions.length)} de {filteredTransactions.length} transações
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-3 py-1 text-sm border rounded-lg ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  )
                })}
              </div>

              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Próxima
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {editingTransaction ? 'Editar Transação' : 'Nova Transação'}
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <input
                  {...register('description', { required: 'Descrição é obrigatória' })}
                  type="text"
                  className="input-field"
                  placeholder="Descrição da transação"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor
                </label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="input-field"
                  placeholder="R$ 0,00"
                />
                {transactionAmount <= 0 && (
                  <p className="mt-1 text-sm text-red-600">Valor deve ser maior que zero</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  {...register('type', { required: 'Tipo é obrigatório' })}
                  className="input-field"
                >
                  <option value="">Selecione o tipo</option>
                  <option value="INCOME">Receita</option>
                  <option value="EXPENSE">Despesa</option>
                  <option value="INVESTMENT">Investimento</option>
                  <option value="LOAN">Empréstimo</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  {...register('categoryId')}
                  className="input-field"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banco
                </label>
                <select
                  {...register('bankId')}
                  className="input-field"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name} - {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(bank.currentBalance)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Forma de Pagamento
                </label>
                <select
                  {...register('paymentMethodId')}
                  className="input-field"
                  onChange={(e) => {
                    const selectedMethod = paymentMethods.find(m => m.id === e.target.value)
                    setSelectedPaymentMethod(selectedMethod)
                  }}
                >
                  <option value="">Selecione uma forma de pagamento</option>
                  {paymentMethods.map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.icon} {method.name}
                      {method.bank && ` (${method.bank.name})`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Parcelamento - apenas para cartões de crédito */}
              {selectedPaymentMethod?.type === 'CREDIT' && !editingTransaction && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Parcelamento
                  </label>
                  <select
                    value={installments}
                    onChange={(e) => setInstallments(parseInt(e.target.value))}
                    className="input-field"
                  >
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num}>
                        {num}x {num > 1 && `de ${new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(transactionAmount / num)}`}
                      </option>
                    ))}
                  </select>
                  {installments > 1 && (
                    <p className="mt-1 text-sm text-blue-600">
                      💳 Será cobrado {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(transactionAmount / installments)} por mês durante {installments} meses
                    </p>
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data
                </label>
                <input
                  {...register('date')}
                  type="date"
                  className="input-field"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>

              {/* Upload de Comprovante */}
              {!editingTransaction && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Upload className="inline h-4 w-4 mr-1" />
                    Comprovante (Opcional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => setSelectedFile(e.target.files[0])}
                      className="hidden"
                      id="receipt-upload"
                    />
                    <label htmlFor="receipt-upload" className="cursor-pointer">
                      {selectedFile ? (
                        <div className="flex items-center justify-center gap-2 text-green-600">
                          <Upload className="h-5 w-5" />
                          <span className="text-sm font-medium">{selectedFile.name}</span>
                        </div>
                      ) : (
                        <div className="text-gray-500">
                          <Upload className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-sm">Clique para enviar uma foto ou PDF</p>
                          <p className="text-xs text-gray-400 mt-1">PNG, JPG ou PDF até 10MB</p>
                        </div>
                      )}
                    </label>
                  </div>
                  {selectedFile && (
                    <button
                      type="button"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2 text-sm text-red-600 hover:text-red-800"
                    >
                      Remover arquivo
                    </button>
                  )}
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false)
                    setEditingTransaction(null)
                    setTransactionAmount(0)
                    reset()
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                >
                  {editingTransaction ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Comprovante */}
      <ReceiptModal
        isOpen={showReceiptModal}
        onClose={() => setShowReceiptModal(false)}
        receiptUrl={selectedReceipt?.url}
        transactionDescription={selectedReceipt?.description}
      />

      {/* Modal de Parcelamento */}
      <InstallmentModal
        isOpen={showInstallmentModal}
        onClose={() => setShowInstallmentModal(false)}
        transaction={selectedInstallment}
        installments={installmentDetails}
      />
    </div>
  )
}

export default Transactions
