@echo off
echo ========================================
echo    🧪 Teste do Sistema de Empréstimos
echo ========================================
echo.
echo Este script irá testar todas as funcionalidades:
echo.
echo 📋 Funcionalidades a testar:
echo    1. ✅ Criar contatos com foto
echo    2. ✅ Criar empréstimos
echo    3. ✅ Registrar pagamentos
echo    4. ✅ Visualizar cronogramas
echo    5. ✅ Status automático de pagadores
echo    6. ✅ Integração com bancos
echo.
echo 🌐 Acesse: http://localhost:5173/loans
echo.
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.
echo 📝 Roteiro de teste:
echo.
echo 1️⃣  CRIAR CONTATO:
echo    • Clique em "Novo Contato"
echo    • Adicione nome, email, telefone
echo    • Faça upload de uma foto
echo    • Salve o contato
echo.
echo 2️⃣  CRIAR EMPRÉSTIMO:
echo    • Clique em "Novo Empréstimo" ou no botão "+" do contato
echo    • Preencha os dados do empréstimo
echo    • Selecione um banco (opcional)
echo    • Adicione comprovante (opcional)
echo    • Defina parcelas e juros
echo    • Salve o empréstimo
echo.
echo 3️⃣  VISUALIZAR DETALHES:
echo    • Clique em "Ver" no card do contato
echo    • Veja o cronograma de parcelas
echo    • Observe as estatísticas de pagamento
echo    • Verifique o status do contato
echo.
echo 4️⃣  REGISTRAR PAGAMENTO:
echo    • No modal de detalhes, clique em "Pagar" em uma parcela
echo    • Selecione o banco
echo    • Defina a data do pagamento
echo    • Adicione comprovante (opcional)
echo    • Registre o pagamento
echo.
echo 5️⃣  VERIFICAR INTEGRAÇÃO:
echo    • Vá para a página de Transações
echo    • Verifique se as transações foram criadas automaticamente
echo    • Vá para a página de Bancos
echo    • Verifique se os saldos foram atualizados
echo.
echo 🎯 Resultados esperados:
echo    • Status do contato atualiza automaticamente
echo    • Transações são criadas nos bancos
echo    • Saldos dos bancos são atualizados
echo    • Cronograma mostra progresso visual
echo    • Comprovantes são salvos e visualizáveis
echo.
echo ⚠️  Certifique-se de que:
echo    • O banco foi atualizado (update-schema.bat)
echo    • O sistema está rodando (start.bat)
echo    • Você tem bancos cadastrados
echo.
pause
echo.
echo 🚀 Iniciando navegador...
start http://localhost:5173/loans
echo.
echo ✅ Teste iniciado! Siga o roteiro acima.
echo.
pause
