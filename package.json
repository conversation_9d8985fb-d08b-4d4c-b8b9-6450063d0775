{"name": "sara-expense-system", "version": "1.0.0", "description": "Sistema de gastos monetários com React e Node.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"cloudinary": "^2.6.1", "multer": "^2.0.0", "react-beautiful-dnd": "^13.1.1", "react-grid-layout": "^1.5.1"}}