import api from './api'

// Verificar se a API está disponível
if (!api) {
  console.error('API service not available')
}

export const contactService = {
  // Contatos
  async getContacts() {
    const response = await api.get('/contacts')
    return response.data
  },

  async getContact(id) {
    const response = await api.get(`/contacts/${id}`)
    return response.data
  },

  async createContact(contactData) {
    const formData = new FormData()

    Object.keys(contactData).forEach(key => {
      if (contactData[key] !== null && contactData[key] !== undefined) {
        formData.append(key, contactData[key])
      }
    })

    const response = await api.post('/contacts', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async updateContact(id, contactData) {
    const formData = new FormData()

    Object.keys(contactData).forEach(key => {
      if (contactData[key] !== null && contactData[key] !== undefined) {
        formData.append(key, contactData[key])
      }
    })

    const response = await api.put(`/contacts/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async deleteContact(id) {
    const response = await api.delete(`/contacts/${id}`)
    return response.data
  }
}

export const loanService = {
  // Empréstimos
  async getLoans(filters = {}) {
    const params = new URLSearchParams()

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        params.append(key, filters[key])
      }
    })

    const response = await api.get(`/loans?${params.toString()}`)
    return response.data
  },

  async getLoan(id) {
    const response = await api.get(`/loans/${id}`)
    return response.data
  },

  async createLoan(loanData) {
    const formData = new FormData()

    Object.keys(loanData).forEach(key => {
      if (loanData[key] !== null && loanData[key] !== undefined) {
        formData.append(key, loanData[key])
      }
    })

    const response = await api.post('/loans', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async updateLoan(id, loanData) {
    const formData = new FormData()

    Object.keys(loanData).forEach(key => {
      if (loanData[key] !== null && loanData[key] !== undefined) {
        formData.append(key, loanData[key])
      }
    })

    const response = await api.put(`/loans/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async deleteLoan(id) {
    const response = await api.delete(`/loans/${id}`)
    return response.data
  },

  async registerPayment(loanId, paymentData) {
    const formData = new FormData()

    Object.keys(paymentData).forEach(key => {
      if (paymentData[key] !== null && paymentData[key] !== undefined) {
        formData.append(key, paymentData[key])
      }
    })

    const response = await api.post(`/loans/${loanId}/payment`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async markLoanAsCompleted(loanId) {
    const response = await api.patch(`/loans/${loanId}/complete`)
    return response.data
  },

  async deleteLoanById(loanId) {
    const response = await api.delete(`/loans/${loanId}`)
    return response.data
  }
}

// Funções utilitárias
export const loanUtils = {
  getStatusLabel(status) {
    const labels = {
      'ACTIVE': 'Ativo',
      'COMPLETED': 'Quitado',
      'OVERDUE': 'Em Atraso',
      'CANCELLED': 'Cancelado'
    }
    return labels[status] || status
  },

  getStatusColor(status) {
    const colors = {
      'ACTIVE': 'blue',
      'COMPLETED': 'green',
      'OVERDUE': 'red',
      'CANCELLED': 'gray'
    }
    return colors[status] || 'gray'
  },

  getTypeLabel(type) {
    const labels = {
      'LOAN_GIVEN': 'Emprestei',
      'LOAN_RECEIVED': 'Peguei Emprestado'
    }
    return labels[type] || type
  },

  getLoanTypeLabel(loanType) {
    const labels = {
      'MONEY': 'Dinheiro',
      'CREDIT_CARD': 'Compra no Cartão',
      'OTHER': 'Outro'
    }
    return labels[loanType] || loanType
  },

  getContactStatusLabel(status) {
    const labels = {
      'GOOD': 'Bom Pagador',
      'NEUTRAL': 'Neutro',
      'BAD': 'Mau Pagador'
    }
    return labels[status] || status
  },

  getContactStatusColor(status) {
    const colors = {
      'GOOD': 'green',
      'NEUTRAL': 'gray',
      'BAD': 'red'
    }
    return colors[status] || 'gray'
  },

  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  },

  formatDate(date) {
    return new Date(date).toLocaleDateString('pt-BR')
  },

  calculateProgress(paidInstallments, totalInstallments) {
    return Math.round((paidInstallments / totalInstallments) * 100)
  },

  isOverdue(dueDate, isPaid) {
    if (isPaid) return false
    return new Date(dueDate) < new Date()
  },

  getNextPaymentDate(payments) {
    const unpaidPayments = payments.filter(p => !p.isPaid)
    if (unpaidPayments.length === 0) return null

    return unpaidPayments.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))[0].dueDate
  }
}
