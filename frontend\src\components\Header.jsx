import React, { useState } from 'react'
import { Menu, ChevronDown } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useLocation } from 'react-router-dom'

function Header({ onMenuClick, selectedYear, onYearChange }) {
  const { user } = useAuth()
  const location = useLocation()
  const [showYearDropdown, setShowYearDropdown] = useState(false)

  // Definir título baseado na rota
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'Página Inicial'
      case '/transactions':
        return 'Transações'
      case '/banks':
        return 'Bancos'
      case '/categories':
        return 'Categorias'
      case '/loans':
        return 'Empréstimos e Dívidas'
      case '/dashboard':
        return 'Dashboard'
      default:
        return 'Sara - Sistema Financeiro'
    }
  }

  // Verificar se deve mostrar o filtro de ano
  const shouldShowYearFilter = location.pathname === '/' || location.pathname === '/dashboard'

  // Gerar anos de 2020 até o ano atual + 1
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 2019 + 2 }, (_, i) => currentYear + 1 - i)

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Menu button and title */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="ml-4 lg:ml-0">
            <h1 className="text-2xl font-semibold text-gray-900">
              {getPageTitle()}
            </h1>
          </div>
        </div>

        {/* Right side - Year selector (apenas na página inicial e dashboard) */}
        <div className="flex items-center space-x-4">
          {shouldShowYearFilter && (
            <div className="relative">
              <button
                onClick={() => setShowYearDropdown(!showYearDropdown)}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md"
              >
                <span className="font-medium">{selectedYear || currentYear}</span>
                <ChevronDown className="ml-2 h-4 w-4" />
              </button>

              {showYearDropdown && (
                <div className="absolute right-0 mt-2 w-36 bg-white rounded-lg shadow-xl border border-gray-200 z-10 overflow-hidden">
                  <div className="py-1">
                    {years.map((year) => (
                      <button
                        key={year}
                        onClick={() => {
                          if (onYearChange) {
                            onYearChange(year)
                          }
                          setShowYearDropdown(false)
                        }}
                        className={`block w-full text-left px-4 py-3 text-sm hover:bg-blue-50 transition-colors ${
                          year === (selectedYear || currentYear)
                            ? 'bg-blue-50 font-semibold text-blue-700 border-r-4 border-blue-500'
                            : 'text-gray-700'
                        }`}
                      >
                        {year}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header
