import React, { useState } from 'react'
import { Menu, ChevronDown } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

function Header({ onMenuClick, selectedYear, onYearChange }) {
  const { user } = useAuth()
  const [showYearDropdown, setShowYearDropdown] = useState(false)

  // Gerar anos de 2020 até o ano atual + 1
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 2019 + 2 }, (_, i) => currentYear + 1 - i)

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Menu button and title */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="ml-4 lg:ml-0">
            <h1 className="text-2xl font-semibold text-gray-900">
              Página Inicial
            </h1>
          </div>
        </div>

        {/* Right side - Year selector */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <button
              onClick={() => setShowYearDropdown(!showYearDropdown)}
              className="flex items-center px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <span className="font-medium">{selectedYear || currentYear}</span>
              <ChevronDown className="ml-2 h-4 w-4" />
            </button>

            {showYearDropdown && (
              <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                {years.map((year) => (
                  <button
                    key={year}
                    onClick={() => {
                      if (onYearChange) {
                        onYearChange(year)
                      }
                      setShowYearDropdown(false)
                    }}
                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                      year === (selectedYear || currentYear) ? 'bg-gray-50 font-medium' : ''
                    }`}
                  >
                    {year}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* User info */}
          <div className="hidden sm:flex items-center">
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user?.name?.charAt(0).toUpperCase()}
              </span>
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700">
              {user?.name}
            </span>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
