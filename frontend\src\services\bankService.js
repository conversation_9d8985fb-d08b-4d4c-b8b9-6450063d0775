import api from './api'

export const bankService = {
  // Listar bancos
  async getBanks() {
    const response = await api.get('/banks')
    return response.data
  },

  // Criar banco
  async createBank(bankData) {
    const response = await api.post('/banks', bankData)
    return response.data
  },

  // Atualizar banco
  async updateBank(id, bankData) {
    const response = await api.put(`/banks/${id}`, bankData)
    return response.data
  },

  // Deletar banco
  async deleteBank(id) {
    const response = await api.delete(`/banks/${id}`)
    return response.data
  },

  // Obter saldo total
  async getTotalBalance() {
    const response = await api.get('/banks/balance')
    return response.data
  },

  // Transferir entre bancos
  async transferBetweenBanks(transferData) {
    const response = await api.post('/banks/transfer', transferData)
    return response.data
  }
}

export const paymentMethodService = {
  // Listar formas de pagamento
  async getPaymentMethods() {
    const response = await api.get('/payment-methods')
    return response.data
  },

  // Criar forma de pagamento
  async createPaymentMethod(methodData) {
    const response = await api.post('/payment-methods', methodData)
    return response.data
  },

  // Atualizar forma de pagamento
  async updatePaymentMethod(id, methodData) {
    const response = await api.put(`/payment-methods/${id}`, methodData)
    return response.data
  },

  // Deletar forma de pagamento
  async deletePaymentMethod(id) {
    const response = await api.delete(`/payment-methods/${id}`)
    return response.data
  },

  // Criar formas de pagamento padrão
  async createDefaults() {
    const response = await api.post('/payment-methods/create-defaults')
    return response.data
  }
}
