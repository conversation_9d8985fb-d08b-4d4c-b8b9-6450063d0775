@echo off
echo ========================================
echo    🔄 Atualizando Schema de Empréstimos
echo ========================================
echo.
echo Aplicando mudanças no banco de dados...
echo.

cd backend

echo 1. Verificando schema atual...
if exist "prisma\schema.prisma" (
    echo ✅ Schema encontrado
) else (
    echo ❌ Schema não encontrado
    pause
    exit /b 1
)

echo.
echo 2. Aplicando mudanças no banco...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo.
echo 3. Gerando cliente Prisma...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo.
echo 4. Verificando tabelas criadas...
echo Tabelas de empréstimos:
echo   • Contact - Contatos
echo   • Loan - Empréstimos
echo   • LoanPayment - Parcelas de pagamento
echo.

echo ✅ Schema atualizado com sucesso!
echo.
echo 📋 Funcionalidades disponíveis:
echo   • Criar contatos com fotos
echo   • Criar empréstimos com parcelas
echo   • Registrar pagamentos
echo   • Integração automática com transações
echo   • Status automático de pagadores
echo.
echo 🔄 Reiniciando servidor...
cd ..

echo.
echo 🚀 Para testar o sistema completo:
echo   1. Execute: start.bat
echo   2. Acesse: http://localhost:5173/loans
echo   3. Crie um contato
echo   4. Crie um empréstimo
echo   5. Registre pagamentos
echo   6. Veja as transações automáticas
echo.
pause
