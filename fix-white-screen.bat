@echo off
echo ========================================
echo    🔧 Correção de Tela Branca
echo ========================================
echo.
echo Diagnosticando e corrigindo tela branca...
echo.

echo 1. Parando todos os processos Node.js...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo.
echo 2. Verificando estrutura de arquivos...
cd frontend

if not exist "src\main.jsx" (
    echo ❌ main.jsx não encontrado
    echo Criando main.jsx básico...
    echo import React from 'react' > src\main.jsx
    echo import ReactDOM from 'react-dom/client' >> src\main.jsx
    echo import AppSimple from './AppSimple.jsx' >> src\main.jsx
    echo import './index.css' >> src\main.jsx
    echo. >> src\main.jsx
    echo ReactDOM.createRoot(document.getElementById('root'^)^).render( >> src\main.jsx
    echo   ^<React.StrictMode^> >> src\main.jsx
    echo     ^<AppSimple /^> >> src\main.jsx
    echo   ^</React.StrictMode^>, >> src\main.jsx
    echo ^) >> src\main.jsx
)

if not exist "src\AppSimple.jsx" (
    echo ❌ AppSimple.jsx não encontrado
    echo Arquivo será criado automaticamente...
)

echo.
echo 3. Verificando dependências críticas...
echo Verificando package.json...
if not exist "package.json" (
    echo ❌ package.json não encontrado!
    echo Execute install.bat primeiro
    pause
    exit /b 1
)

echo.
echo 4. Limpando cache e reinstalando...
echo Removendo node_modules...
if exist "node_modules" rmdir /s /q node_modules 2>nul
if exist "package-lock.json" del package-lock.json 2>nul

echo Limpando cache npm...
call npm cache clean --force 2>nul

echo Instalando dependências...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erro na instalação das dependências
    pause
    exit /b 1
)

echo.
echo 5. Verificando Tailwind CSS...
echo Verificando se Tailwind está configurado...
if not exist "tailwind.config.js" (
    echo ❌ tailwind.config.js não encontrado
    echo Criando configuração básica...
    echo export default { > tailwind.config.js
    echo   content: ["./index.html", "./src/**/*.{js,jsx}"], >> tailwind.config.js
    echo   theme: { extend: {} }, >> tailwind.config.js
    echo   plugins: [], >> tailwind.config.js
    echo } >> tailwind.config.js
)

echo.
echo 6. Verificando index.html...
if not exist "index.html" (
    echo ❌ index.html não encontrado
    echo Criando index.html básico...
    echo ^<!DOCTYPE html^> > index.html
    echo ^<html lang="pt-BR"^> >> index.html
    echo ^<head^> >> index.html
    echo   ^<meta charset="UTF-8" /^> >> index.html
    echo   ^<meta name="viewport" content="width=device-width, initial-scale=1.0" /^> >> index.html
    echo   ^<title^>Sara - Sistema Financeiro^</title^> >> index.html
    echo ^</head^> >> index.html
    echo ^<body^> >> index.html
    echo   ^<div id="root"^>^</div^> >> index.html
    echo   ^<script type="module" src="/src/main.jsx"^>^</script^> >> index.html
    echo ^</body^> >> index.html
    echo ^</html^> >> index.html
)

echo.
echo 7. Testando compilação...
echo Verificando se o projeto compila...
call npm run build 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  Erro na compilação, mas continuando...
)

echo.
echo ========================================
echo    ✅ Correções Aplicadas
echo ========================================
echo.
echo 🔧 Mudanças realizadas:
echo    • AuthContext com timeout de segurança
echo    • AppSimple para diagnóstico
echo    • Dependências reinstaladas
echo    • Cache limpo
echo    • Configurações verificadas
echo.
echo 🚀 Iniciando servidor de desenvolvimento...
echo.
echo 📍 URLs para testar:
echo    • http://localhost:5173/test (Página de teste)
echo    • http://localhost:5173/login (Login simples)
echo    • http://localhost:5173/dashboard (Dashboard simples)
echo.
echo ⚠️  Se ainda aparecer tela branca:
echo    1. Abra F12 no navegador
echo    2. Vá na aba Console
echo    3. Procure erros em vermelho
echo    4. Vá na aba Network
echo    5. Recarregue a página (Ctrl+R)
echo    6. Veja se algum arquivo falhou ao carregar
echo.
echo 🔄 Para voltar ao sistema completo depois:
echo    1. Edite src/main.jsx
echo    2. Mude AppSimple para App
echo    3. Reinicie o servidor
echo.

timeout /t 3 /nobreak >nul
echo Abrindo navegador...
start http://localhost:5173/test

echo.
echo 🚀 Iniciando servidor...
call npm run dev

cd ..
