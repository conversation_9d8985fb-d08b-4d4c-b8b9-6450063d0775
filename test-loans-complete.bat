@echo off
echo ========================================
echo    🧪 Teste Completo de Empréstimos
echo ========================================
echo.
echo Este script testa todas as funcionalidades corrigidas:
echo.
echo ✅ Correções aplicadas:
echo    1. Menu lateral duplo corrigido
echo    2. Banco obrigatório em empréstimos
echo    3. Transações automáticas implementadas
echo    4. Pagamentos geram transações
echo.
echo 📋 Roteiro de teste completo:
echo.
echo 🏦 1. PREPARAÇÃO:
echo    • Certifique-se de ter bancos cadastrados
echo    • Vá em Bancos e crie pelo menos 1 banco
echo    • Anote o nome do banco para usar nos testes
echo.
echo 👤 2. CRIAR CONTATO:
echo    • Acesse: Empréstimos → Novo Contato
echo    • Preencha: Nome, Email, Telefone
echo    • Adicione uma foto (opcional)
echo    • Salve o contato
echo.
echo 💰 3. CRIAR EMPRÉSTIMO:
echo    • Clique no botão "+" do contato OU "Novo Empréstimo"
echo    • Preencha todos os campos obrigatórios:
echo      - Título: "Empréstimo para emergência"
echo      - Tipo: "Emprestei" ou "Peguei Emprestado"
echo      - Valor: R$ 1.000,00
echo      - Parcelas: 3
echo      - Taxa de juros: 2%%
echo      - Banco: OBRIGATÓRIO (selecione um banco)
echo    • Adicione comprovante (opcional)
echo    • Salve o empréstimo
echo.
echo 📊 4. VERIFICAR TRANSAÇÃO AUTOMÁTICA:
echo    • Vá para a aba "Transações"
echo    • Verifique se apareceu uma transação:
echo      - Descrição: "Empréstimo para [nome]"
echo      - Valor: R$ 1.000,00
echo      - Tipo: Despesa (se emprestou) ou Receita (se pegou)
echo      - Banco: O banco selecionado
echo.
echo 👁️ 5. VISUALIZAR CRONOGRAMA:
echo    • Volte para Empréstimos
echo    • Clique em "Ver" no card do contato
echo    • Observe o cronograma de parcelas
echo    • Veja as estatísticas de pagamento
echo.
echo 💳 6. REGISTRAR PAGAMENTO:
echo    • No modal de detalhes, clique "Pagar" em uma parcela
echo    • Preencha:
echo      - Valor pago: R$ 340,00 (valor calculado automaticamente)
echo      - Data do pagamento: hoje
echo      - Banco: OBRIGATÓRIO (selecione um banco)
echo    • Adicione comprovante (opcional)
echo    • Registre o pagamento
echo.
echo 📈 7. VERIFICAR SEGUNDA TRANSAÇÃO:
echo    • Vá novamente para "Transações"
echo    • Verifique se apareceu nova transação:
echo      - Descrição: "Pagamento: [título] (1/3)"
echo      - Valor: R$ 340,00
echo      - Tipo: Receita (se emprestou) ou Despesa (se pegou)
echo      - Banco: O banco selecionado
echo.
echo 🏦 8. VERIFICAR SALDOS DOS BANCOS:
echo    • Vá para "Bancos"
echo    • Verifique se os saldos foram atualizados:
echo      - Empréstimo inicial: saldo diminuiu/aumentou
echo      - Pagamento: saldo aumentou/diminuiu
echo.
echo 📊 9. VERIFICAR STATUS DO CONTATO:
echo    • Volte para "Empréstimos"
echo    • Observe se o status do contato mudou:
echo      - Verde: Bom pagador (pagou em dia)
echo      - Amarelo: Neutro
echo      - Vermelho: Mau pagador (pagou atrasado)
echo.
echo 🔄 10. TESTE DE ATRASO:
echo    • Registre um pagamento com data passada
echo    • Veja se o sistema marca como "atrasado"
echo    • Observe como afeta o status do contato
echo.
echo ⚠️  PONTOS IMPORTANTES:
echo    • Banco é OBRIGATÓRIO em empréstimos
echo    • Banco é OBRIGATÓRIO em pagamentos
echo    • Transações são criadas AUTOMATICAMENTE
echo    • Saldos dos bancos são atualizados AUTOMATICAMENTE
echo    • Status do contato é calculado AUTOMATICAMENTE
echo.
echo 🎯 RESULTADOS ESPERADOS:
echo    ✅ Menu lateral aparece apenas uma vez
echo    ✅ Não é possível criar empréstimo sem banco
echo    ✅ Cada empréstimo gera uma transação
echo    ✅ Cada pagamento gera uma transação
echo    ✅ Saldos dos bancos são atualizados
echo    ✅ Status dos contatos é calculado automaticamente
echo    ✅ Cronograma visual funciona perfeitamente
echo.
pause
echo.
echo 🚀 Abrindo sistema para teste...
start http://localhost:5173/loans
echo.
echo 📝 Siga o roteiro acima para testar todas as funcionalidades!
echo.
pause
