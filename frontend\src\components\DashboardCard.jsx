import React, { useState, useEffect } from 'react'
import {
  MoreVertical,
  Trash2,
  Settings,
  Eye,
  EyeOff,
  GripVertical,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ChevronUp,
  ChevronDown,
  Move3D
} from 'lucide-react'
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'
import api from '../services/api'

function DashboardCard({
  card,
  onUpdate,
  onDelete,
  onShowTransactions,
  isDragging,
  onDragStart,
  onDragEnd,
  onResize,
  onEdit,
  style
}) {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showMenu, setShowMenu] = useState(false)
  const [sortField, setSortField] = useState('date')
  const [sortDirection, setSortDirection] = useState('desc')

  useEffect(() => {
    fetchCardData()
  }, [card.id, card.categories, card.selectedYear, card.selectedMonth])

  const fetchCardData = async () => {
    try {
      setLoading(true)
      const year = card.selectedYear || new Date().getFullYear()
      const month = card.selectedMonth || new Date().getMonth() + 1
      const response = await api.get(`/dashboard-cards/${card.id}/data?year=${year}&month=${month}`)
      setData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados do card:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getCategoryNames = () => {
    if (!data?.categoryNames) return 'Todas as categorias'
    if (data.categoryNames.length <= 2) {
      return data.categoryNames.join(', ')
    }
    return `${data.categoryNames.slice(0, 2).join(', ')} +${data.categoryNames.length - 2}`
  }

  const getPeriodText = () => {
    return card.config?.period === 'month' ? 'Mensal' : 'Anual'
  }

  const handleCardClick = () => {
    if (card.type === 'numeric' && data?.allTransactions) {
      onShowTransactions(data.allTransactions, card.title, data.total)
    }
  }

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortedTransactions = (transactions) => {
    if (!transactions) return []

    return [...transactions].sort((a, b) => {
      let aValue, bValue

      switch (sortField) {
        case 'date':
          aValue = new Date(a.date)
          bValue = new Date(b.date)
          break
        case 'amount':
          aValue = a.amount
          bValue = b.amount
          break
        case 'description':
          aValue = a.description.toLowerCase()
          bValue = b.description.toLowerCase()
          break
        case 'category':
          aValue = a.category?.name?.toLowerCase() || ''
          bValue = b.category?.name?.toLowerCase() || ''
          break
        default:
          return 0
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  const renderCardContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )
    }

    switch (card.type) {
      case 'numeric':
        const changeValue = data?.change || 0
        const isPositive = changeValue >= 0
        return (
          <div
            className="text-center cursor-pointer hover:bg-slate-50 rounded-lg p-4 transition-colors"
            onClick={handleCardClick}
          >
            <div className="text-3xl font-bold text-gray-900 mb-2">
              {formatCurrency(data?.total || 0)}
            </div>
            <div className="text-sm text-gray-600 mb-2">
              {data?.count || 0} transações
            </div>

            {/* Comparação com período anterior */}
            {data?.previousTotal !== undefined && (
              <div className="mb-3 p-2 bg-gray-50 rounded-lg">
                <div className="text-xs text-gray-500 mb-1">
                  Período anterior: {formatCurrency(data.previousTotal)}
                </div>
                <div className={`flex items-center justify-center text-sm font-medium ${
                  isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  {isPositive ? (
                    <TrendingUp className="h-4 w-4 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 mr-1" />
                  )}
                  {isPositive ? '+' : ''}{changeValue.toFixed(1)}%
                </div>
              </div>
            )}

            <div className="flex items-center justify-center text-xs text-slate-600">
              <DollarSign className="h-4 w-4 mr-1" />
              Clique para ver detalhes
            </div>
          </div>
        )

      case 'pie':
        return (
          <div className="h-80 flex flex-col">
            {/* Gráfico */}
            <div className="flex-1 min-h-0">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data?.chartData || []}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    label={({ percent }) => percent > 0.05 ? `${(percent * 100).toFixed(1)}%` : ''}
                    labelLine={false}
                  >
                    {(data?.chartData || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), 'Valor']}
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Legenda compacta e total */}
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="text-center mb-2">
                <span className="text-sm font-semibold text-gray-900">
                  Total: {formatCurrency(data?.total || 0)}
                </span>
              </div>
              <div className="flex flex-wrap gap-x-3 gap-y-1 justify-center">
                {(data?.chartData || []).map((entry, index) => (
                  <div key={index} className="flex items-center space-x-1">
                    <div
                      className="w-3 h-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className="text-xs text-gray-600 truncate max-w-24" title={`${entry.icon} ${entry.name}`}>
                      {entry.icon} {entry.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'table':
        const sortedTransactions = getSortedTransactions(data?.transactions || [])
        return (
          <div className="h-80 flex flex-col">
            <div className="flex-1 overflow-hidden">
              <div className="h-full overflow-auto">
                <table className="min-w-full">
                  <thead className="bg-gradient-to-r from-slate-50 to-slate-100 sticky top-0">
                    <tr>
                      <th
                        className="px-3 py-2 text-left text-xs font-medium text-slate-700 uppercase cursor-pointer hover:bg-slate-200 transition-colors"
                        onClick={() => handleSort('description')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Descrição</span>
                          {sortField === 'description' && (
                            sortDirection === 'asc' ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />
                          )}
                        </div>
                      </th>
                      <th
                        className="px-3 py-2 text-left text-xs font-medium text-slate-700 uppercase cursor-pointer hover:bg-slate-200 transition-colors"
                        onClick={() => handleSort('category')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Categoria</span>
                          {sortField === 'category' && (
                            sortDirection === 'asc' ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />
                          )}
                        </div>
                      </th>
                      <th
                        className="px-3 py-2 text-left text-xs font-medium text-slate-700 uppercase cursor-pointer hover:bg-slate-200 transition-colors"
                        onClick={() => handleSort('date')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Data</span>
                          {sortField === 'date' && (
                            sortDirection === 'asc' ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />
                          )}
                        </div>
                      </th>
                      <th
                        className="px-3 py-2 text-right text-xs font-medium text-slate-700 uppercase cursor-pointer hover:bg-slate-200 transition-colors"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center justify-end space-x-1">
                          <span>Valor</span>
                          {sortField === 'amount' && (
                            sortDirection === 'asc' ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />
                          )}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {sortedTransactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-slate-50 transition-colors">
                        <td className="px-3 py-2 text-sm text-gray-900 truncate max-w-32">
                          {transaction.description}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <span>{transaction.category?.icon || '📊'}</span>
                            <span className="truncate max-w-20">{transaction.category?.name || 'Sem categoria'}</span>
                          </div>
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-600">
                          {new Date(transaction.date).toLocaleDateString('pt-BR')}
                        </td>
                        <td className="px-3 py-2 text-sm text-right font-medium">
                          <span className={transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'}>
                            {formatCurrency(transaction.amount)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <div className="border-t border-gray-200 p-2 bg-gray-50">
              <div className="text-center text-xs text-gray-500">
                {data?.count || 0} transações • Total: {formatCurrency(data?.total || 0)}
              </div>
            </div>
          </div>
        )

      case 'chart':
        const categories = data?.categories || []
        const categoryColors = data?.categoryColors || {}

        return (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data?.chartData || []} margin={{ left: 20, right: 20, top: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="formattedDate"
                  tick={{ fontSize: 10 }}
                  interval="preserveStartEnd"
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis
                  tickFormatter={(value) => {
                    if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
                    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
                    return `R$ ${value.toFixed(0)}`
                  }}
                  axisLine={false}
                  tickLine={false}
                  width={60}
                />
                <Tooltip
                  formatter={(value, name) => [formatCurrency(value), name]}
                  labelFormatter={(label) => `Data: ${label}`}
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                {categories.map((categoryName, index) => (
                  <Line
                    key={categoryName}
                    type="monotone"
                    dataKey={categoryName}
                    stroke={categoryColors[categoryName] || `hsl(${index * 60}, 70%, 50%)`}
                    strokeWidth={2}
                    strokeDasharray={index > 0 ? `${5 + index * 2} ${3 + index}` : '0'}
                    dot={{ fill: categoryColors[categoryName] || `hsl(${index * 60}, 70%, 50%)`, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: categoryColors[categoryName] || `hsl(${index * 60}, 70%, 50%)`, strokeWidth: 2 }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>

            {/* Legenda das categorias */}
            {categories.length > 1 && (
              <div className="mt-2 flex flex-wrap gap-2">
                {categories.map((categoryName, index) => (
                  <div key={categoryName} className="flex items-center space-x-1">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor: categoryColors[categoryName] || `hsl(${index * 60}, 70%, 50%)`,
                        border: index > 0 ? `2px dashed ${categoryColors[categoryName] || `hsl(${index * 60}, 70%, 50%)`}` : 'none'
                      }}
                    />
                    <span className="text-xs text-gray-600">{categoryName}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Tipo de card não suportado
          </div>
        )
    }
  }

  return (
    <div
      className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 select-none ${
        isDragging ? 'shadow-xl scale-105 border-slate-400' : 'hover:shadow-md'
      }`}
      style={{ ...style, userSelect: 'none' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-slate-50 to-slate-100">
        <div className="flex items-center space-x-2">
          <div
            className="p-1 hover:bg-slate-200 rounded cursor-move transition-colors"
            onMouseDown={(e) => {
              if (onDragStart) {
                onDragStart(e)
              }
            }}
            title="Arrastar card"
          >
            <GripVertical className="h-4 w-4 text-slate-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 truncate">{card.title}</h3>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {getPeriodText()}
              </span>
              <span className="text-xs text-gray-500 truncate max-w-32" title={getCategoryNames()}>
                {getCategoryNames()}
              </span>
            </div>
          </div>
        </div>

        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation()
              setShowMenu(!showMenu)
            }}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
          >
            <MoreVertical className="h-4 w-4 text-gray-600" />
          </button>

          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowMenu(false)
                    if (onEdit) {
                      onEdit(card)
                    }
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Configurar
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowMenu(false)
                    onDelete(card.id)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remover
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {renderCardContent()}
      </div>

      {/* Resize Handles */}
      <div
        className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize bg-slate-300 hover:bg-slate-400 transition-colors opacity-50 hover:opacity-100"
        onMouseDown={(e) => {
          e.preventDefault()
          e.stopPropagation()
          if (onResize) {
            onResize(card, e, 'se')
          }
        }}
        title="Redimensionar"
      >
        <Move3D className="w-3 h-3 text-slate-600 m-0.5" />
      </div>

      <div
        className="absolute bottom-0 right-2 left-2 h-1 cursor-s-resize bg-slate-300 hover:bg-slate-400 transition-colors opacity-0 hover:opacity-100"
        onMouseDown={(e) => {
          e.preventDefault()
          e.stopPropagation()
          if (onResize) {
            onResize(card, e, 's')
          }
        }}
        title="Redimensionar altura"
      />

      <div
        className="absolute top-2 bottom-2 right-0 w-1 cursor-e-resize bg-slate-300 hover:bg-slate-400 transition-colors opacity-0 hover:opacity-100"
        onMouseDown={(e) => {
          e.preventDefault()
          e.stopPropagation()
          if (onResize) {
            onResize(card, e, 'e')
          }
        }}
        title="Redimensionar largura"
      />
    </div>
  )
}

export default DashboardCard
