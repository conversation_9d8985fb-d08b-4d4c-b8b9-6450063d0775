import React, { useState, useEffect } from 'react'
import { 
  MoreVertical, 
  Trash2, 
  Settings, 
  Eye, 
  EyeOff,
  GripVertical,
  TrendingUp,
  TrendingDown,
  DollarSign
} from 'lucide-react'
import { 
  <PERSON>Chart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from 'recharts'
import api from '../services/api'

function DashboardCard({ 
  card, 
  onUpdate, 
  onDelete, 
  onShowTransactions,
  isDragging,
  onDragStart,
  onDragEnd,
  style 
}) {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showMenu, setShowMenu] = useState(false)

  useEffect(() => {
    fetchCardData()
  }, [card.id, card.categories])

  const fetchCardData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/dashboard-cards/${card.id}/data`)
      setData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados do card:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const handleCardClick = () => {
    if (card.type === 'numeric' && data?.allTransactions) {
      onShowTransactions(data.allTransactions, card.title, data.total)
    }
  }

  const renderCardContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )
    }

    switch (card.type) {
      case 'numeric':
        return (
          <div 
            className="text-center cursor-pointer hover:bg-gray-50 rounded-lg p-4 transition-colors"
            onClick={handleCardClick}
          >
            <div className="text-3xl font-bold text-gray-900 mb-2">
              {formatCurrency(data?.total || 0)}
            </div>
            <div className="text-sm text-gray-600 mb-2">
              {data?.count || 0} transações
            </div>
            <div className="flex items-center justify-center text-xs text-gray-500">
              <DollarSign className="h-4 w-4 mr-1" />
              Clique para ver detalhes
            </div>
          </div>
        )

      case 'pie':
        return (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data?.chartData || []}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {(data?.chartData || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [formatCurrency(value), 'Valor']} />
              </PieChart>
            </ResponsiveContainer>
            <div className="text-center mt-2">
              <div className="text-lg font-semibold text-gray-900">
                Total: {formatCurrency(data?.total || 0)}
              </div>
            </div>
          </div>
        )

      case 'table':
        return (
          <div className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Descrição
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Data
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase">
                      Valor
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {(data?.transactions || []).slice(0, 5).map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-3 py-2 text-sm text-gray-900 truncate max-w-32">
                        {transaction.description}
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-600">
                        {new Date(transaction.date).toLocaleDateString('pt-BR')}
                      </td>
                      <td className="px-3 py-2 text-sm text-right font-medium">
                        <span className={transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'}>
                          {formatCurrency(transaction.amount)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {(data?.count || 0) > 5 && (
              <div className="text-center py-2 text-xs text-gray-500">
                +{data.count - 5} transações
              </div>
            )}
          </div>
        )

      case 'chart':
        return (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data?.chartData || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="formattedDate" 
                  tick={{ fontSize: 10 }}
                  interval="preserveStartEnd"
                />
                <YAxis tickFormatter={(value) => formatCurrency(value)} />
                <Tooltip 
                  formatter={(value) => [formatCurrency(value), 'Valor']}
                  labelFormatter={(label) => `Data: ${label}`}
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke={card.config.color || '#3B82F6'} 
                  strokeWidth={2}
                  dot={{ fill: card.config.color || '#3B82F6', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Tipo de card não suportado
          </div>
        )
    }
  }

  return (
    <div
      className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 ${
        isDragging ? 'shadow-lg scale-105 rotate-2' : 'hover:shadow-md'
      }`}
      style={style}
      onMouseDown={onDragStart}
      onMouseUp={onDragEnd}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gray-50">
        <div className="flex items-center space-x-2">
          <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
          <h3 className="font-semibold text-gray-900 truncate">{card.title}</h3>
        </div>
        
        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
          >
            <MoreVertical className="h-4 w-4 text-gray-600" />
          </button>
          
          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    // TODO: Implementar edição
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Configurar
                </button>
                <button
                  onClick={() => {
                    onDelete(card.id)
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remover
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {renderCardContent()}
      </div>
    </div>
  )
}

export default DashboardCard
