import React, { useState, useEffect } from 'react'
import { X, Save, Palette, Calendar, Tag } from 'lucide-react'
import api from '../services/api'
import { toast } from 'react-hot-toast'

function EditCardModal({ isOpen, onClose, card, onUpdate }) {
  const [formData, setFormData] = useState({
    title: '',
    categories: [],
    config: {
      color: '#475569',
      period: 'year'
    }
  })
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(false)

  const colors = [
    '#475569', '#64748B', '#334155', '#1E293B', '#0F172A', 
    '#374151', '#4B5563', '#6B7280', '#9CA3AF', '#D1D5DB'
  ]

  useEffect(() => {
    if (isOpen && card) {
      setFormData({
        title: card.title || '',
        categories: card.categories || [],
        config: {
          color: card.config?.color || '#475569',
          period: card.config?.period || 'year'
        }
      })
      fetchCategories()
    }
  }, [isOpen, card])

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
      toast.error('Erro ao carregar categorias')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!formData.title.trim()) {
      toast.error('Título é obrigatório')
      return
    }

    if (formData.categories.length === 0) {
      toast.error('Selecione pelo menos uma categoria')
      return
    }

    setLoading(true)
    try {
      const response = await api.put(`/dashboard-cards/${card.id}`, formData)
      onUpdate(response.data)
      toast.success('Card atualizado com sucesso!')
      onClose()
    } catch (error) {
      console.error('Erro ao atualizar card:', error)
      toast.error('Erro ao atualizar card')
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryToggle = (categoryId) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(id => id !== categoryId)
        : [...prev.categories, categoryId]
    }))
  }

  const toggleAllCategories = () => {
    const allSelected = formData.categories.length === categories.length
    setFormData(prev => ({
      ...prev,
      categories: allSelected ? [] : categories.map(cat => cat.id)
    }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Configurar Card</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Título */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Tag className="h-4 w-4 inline mr-1" />
              Título do Card
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
              placeholder="Ex: Gastos com Alimentação"
            />
          </div>

          {/* Período */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Período de Análise
            </label>
            <select
              value={formData.config.period}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                config: { ...prev.config, period: e.target.value }
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
            >
              <option value="month">Mensal</option>
              <option value="year">Anual</option>
            </select>
          </div>

          {/* Cor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Palette className="h-4 w-4 inline mr-1" />
              Cor do Card
            </label>
            <div className="flex flex-wrap gap-2">
              {colors.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    config: { ...prev.config, color }
                  }))}
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.config.color === color
                      ? 'border-gray-800 scale-110'
                      : 'border-gray-300 hover:border-gray-500'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* Categorias */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Categorias
              </label>
              <button
                type="button"
                onClick={toggleAllCategories}
                className="text-xs text-slate-600 hover:text-slate-700 font-medium"
              >
                {formData.categories.length === categories.length ? 'Desmarcar Todas' : 'Selecionar Todas'}
              </button>
            </div>
            <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
              <div className="space-y-2">
                {categories.map((category) => (
                  <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.categories.includes(category.id)}
                      onChange={() => handleCategoryToggle(category.id)}
                      className="rounded border-gray-300 text-slate-600 focus:ring-slate-500"
                    />
                    <span className="text-lg">{category.icon}</span>
                    <span className="text-sm text-gray-700">{category.name}</span>
                  </label>
                ))}
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {formData.categories.length} categoria(s) selecionada(s)
            </p>
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-gradient-to-r from-slate-700 to-slate-800 text-white rounded-lg hover:from-slate-800 hover:to-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Salvando...' : 'Salvar'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditCardModal
