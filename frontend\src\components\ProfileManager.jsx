import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Trash2, 
  Star, 
  <PERSON>Off,
  Save,
  X,
  Edit3
} from 'lucide-react'
import api from '../services/api'
import toast from 'react-hot-toast'

function ProfileManager({ 
  currentProfile, 
  onProfileChange, 
  isOpen, 
  onClose 
}) {
  const [profiles, setProfiles] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingProfile, setEditingProfile] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })

  useEffect(() => {
    if (isOpen) {
      fetchProfiles()
    }
  }, [isOpen])

  const fetchProfiles = async () => {
    try {
      setLoading(true)
      const response = await api.get('/dashboard-profiles')
      setProfiles(response.data)
    } catch (error) {
      console.error('Erro ao buscar perfis:', error)
      toast.error('Erro ao carregar perfis')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProfile = async () => {
    if (!formData.name.trim()) {
      toast.error('Nome do perfil é obrigatório')
      return
    }

    try {
      const response = await api.post('/dashboard-profiles', formData)
      setProfiles(prev => [...prev, response.data])
      setFormData({ name: '', description: '' })
      setShowCreateForm(false)
      toast.success('Perfil criado com sucesso!')
    } catch (error) {
      console.error('Erro ao criar perfil:', error)
      toast.error('Erro ao criar perfil')
    }
  }

  const handleUpdateProfile = async () => {
    if (!formData.name.trim()) {
      toast.error('Nome do perfil é obrigatório')
      return
    }

    try {
      const response = await api.put(`/dashboard-profiles/${editingProfile.id}`, formData)
      setProfiles(prev => prev.map(p => p.id === editingProfile.id ? response.data : p))
      setEditingProfile(null)
      setFormData({ name: '', description: '' })
      toast.success('Perfil atualizado com sucesso!')
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error)
      toast.error('Erro ao atualizar perfil')
    }
  }

  const handleDeleteProfile = async (profileId) => {
    if (!confirm('Tem certeza que deseja excluir este perfil?')) return

    try {
      await api.delete(`/dashboard-profiles/${profileId}`)
      setProfiles(prev => prev.filter(p => p.id !== profileId))
      
      // Se o perfil excluído era o atual, selecionar outro
      if (currentProfile?.id === profileId) {
        const remainingProfiles = profiles.filter(p => p.id !== profileId)
        if (remainingProfiles.length > 0) {
          onProfileChange(remainingProfiles[0])
        }
      }
      
      toast.success('Perfil excluído com sucesso!')
    } catch (error) {
      console.error('Erro ao excluir perfil:', error)
      toast.error('Erro ao excluir perfil')
    }
  }

  const handleSetDefault = async (profileId) => {
    try {
      const response = await api.put(`/dashboard-profiles/${profileId}/set-default`)
      setProfiles(prev => prev.map(p => ({
        ...p,
        isDefault: p.id === profileId
      })))
      toast.success('Perfil padrão definido!')
    } catch (error) {
      console.error('Erro ao definir perfil padrão:', error)
      toast.error('Erro ao definir perfil padrão')
    }
  }

  const handleDuplicateProfile = async (profileId) => {
    const name = prompt('Nome do novo perfil:')
    if (!name) return

    try {
      const response = await api.post(`/dashboard-profiles/${profileId}/duplicate`, { name })
      setProfiles(prev => [...prev, response.data])
      toast.success('Perfil duplicado com sucesso!')
    } catch (error) {
      console.error('Erro ao duplicar perfil:', error)
      toast.error('Erro ao duplicar perfil')
    }
  }

  const startEdit = (profile) => {
    setEditingProfile(profile)
    setFormData({
      name: profile.name,
      description: profile.description || ''
    })
  }

  const cancelEdit = () => {
    setEditingProfile(null)
    setFormData({ name: '', description: '' })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <h2 className="text-xl font-bold">Gerenciar Perfis</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Botão Criar Novo */}
          <div className="mb-6">
            <button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo Perfil
            </button>
          </div>

          {/* Formulário de Criação/Edição */}
          {(showCreateForm || editingProfile) && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">
                {editingProfile ? 'Editar Perfil' : 'Criar Novo Perfil'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nome *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Ex: Dashboard Pessoal"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Descrição opcional do perfil"
                    rows={2}
                  />
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={editingProfile ? handleUpdateProfile : handleCreateProfile}
                    className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {editingProfile ? 'Salvar' : 'Criar'}
                  </button>
                  
                  <button
                    onClick={() => {
                      setShowCreateForm(false)
                      cancelEdit()
                    }}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Lista de Perfis */}
          <div className="space-y-3">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              </div>
            ) : (
              profiles.map((profile) => (
                <div
                  key={profile.id}
                  className={`p-4 border rounded-lg transition-all ${
                    currentProfile?.id === profile.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900">{profile.name}</h3>
                        {profile.isDefault && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      
                      {profile.description && (
                        <p className="text-sm text-gray-600 mt-1">{profile.description}</p>
                      )}
                      
                      <p className="text-xs text-gray-500 mt-1">
                        {profile._count?.cards || 0} cards
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onProfileChange(profile)}
                        className="px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                      >
                        Usar
                      </button>
                      
                      <button
                        onClick={() => startEdit(profile)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Editar"
                      >
                        <Edit3 className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => handleDuplicateProfile(profile.id)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Duplicar"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                      
                      {!profile.isDefault && (
                        <button
                          onClick={() => handleSetDefault(profile.id)}
                          className="p-1 text-gray-400 hover:text-yellow-500"
                          title="Definir como padrão"
                        >
                          <StarOff className="h-4 w-4" />
                        </button>
                      )}
                      
                      {profiles.length > 1 && (
                        <button
                          onClick={() => handleDeleteProfile(profile.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Excluir"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileManager
