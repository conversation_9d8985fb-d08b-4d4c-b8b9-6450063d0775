const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter todos os cards do usuário por perfil
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { profileId } = req.query;

    let whereClause = { userId };

    if (profileId) {
      whereClause.profileId = profileId;
    } else {
      // Se não especificar perfil, buscar o perfil padrão
      const defaultProfile = await prisma.dashboardProfile.findFirst({
        where: { userId, isDefault: true }
      });

      if (defaultProfile) {
        whereClause.profileId = defaultProfile.id;
      } else {
        whereClause.profileId = null;
      }
    }

    const cards = await prisma.dashboardCard.findMany({
      where: whereClause,
      orderBy: { createdAt: 'asc' }
    });

    // Parse dos JSONs
    const parsedCards = cards.map(card => ({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    }));

    res.json(parsedCards);
  } catch (error) {
    console.error('Erro ao buscar cards:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo card
router.post('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { title, type, position, config, categories, profileId } = req.body;

    let finalProfileId = profileId;

    // Se não especificar perfil, usar o perfil padrão
    if (!finalProfileId) {
      const defaultProfile = await prisma.dashboardProfile.findFirst({
        where: { userId, isDefault: true }
      });
      finalProfileId = defaultProfile?.id;
    }

    const card = await prisma.dashboardCard.create({
      data: {
        userId,
        profileId: finalProfileId,
        title,
        type,
        position: JSON.stringify(position),
        config: JSON.stringify(config),
        categories: JSON.stringify(categories)
      }
    });

    res.json({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    });
  } catch (error) {
    console.error('Erro ao criar card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar card
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { title, type, position, config, categories, visible } = req.body;

    const card = await prisma.dashboardCard.update({
      where: {
        id,
        userId // Garantir que o usuário só pode editar seus próprios cards
      },
      data: {
        title,
        type,
        position: JSON.stringify(position),
        config: JSON.stringify(config),
        categories: JSON.stringify(categories),
        visible
      }
    });

    res.json({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    });
  } catch (error) {
    console.error('Erro ao atualizar card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar posições de múltiplos cards
router.put('/positions/bulk', async (req, res) => {
  try {
    const userId = req.user.id;
    const { cards } = req.body;

    const updatePromises = cards.map(card =>
      prisma.dashboardCard.update({
        where: {
          id: card.id,
          userId
        },
        data: {
          position: JSON.stringify(card.position)
        }
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Posições atualizadas com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar posições:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar card
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    await prisma.dashboardCard.delete({
      where: {
        id,
        userId
      }
    });

    res.json({ message: 'Card removido com sucesso' });
  } catch (error) {
    console.error('Erro ao remover card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter dados de um card específico
router.get('/:id/data', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;

    // Buscar o card
    const card = await prisma.dashboardCard.findFirst({
      where: { id, userId }
    });

    if (!card) {
      return res.status(404).json({ error: 'Card não encontrado' });
    }

    const categories = JSON.parse(card.categories);
    const config = JSON.parse(card.config);

    // Definir período baseado no tipo de filtro
    let startDate, endDate;

    if (config.period === 'month') {
      startDate = new Date(year, month - 1, 1);
      endDate = new Date(year, month, 0, 23, 59, 59);
    } else {
      startDate = new Date(year, 0, 1);
      endDate = new Date(year, 11, 31, 23, 59, 59);
    }

    // Buscar transações
    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        categoryId: { in: categories },
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        category: true
      },
      orderBy: { date: 'desc' }
    });

    // Calcular dados baseado no tipo do card
    let data = {};

    if (card.type === 'numeric') {
      const total = transactions.reduce((sum, t) => sum + t.amount, 0);
      const count = transactions.length;

      data = {
        total,
        count,
        transactions: transactions.slice(0, 10), // Últimas 10 para preview
        allTransactions: transactions
      };
    } else if (card.type === 'pie') {
      const categoryTotals = {};

      transactions.forEach(t => {
        const catName = t.category?.name || 'Sem categoria';
        if (!categoryTotals[catName]) {
          categoryTotals[catName] = {
            name: catName,
            value: 0,
            color: t.category?.color || '#6B7280',
            icon: t.category?.icon || '📊'
          };
        }
        categoryTotals[catName].value += t.amount;
      });

      data = {
        chartData: Object.values(categoryTotals),
        total: transactions.reduce((sum, t) => sum + t.amount, 0)
      };
    } else if (card.type === 'table') {
      data = {
        transactions: transactions.slice(0, 20),
        total: transactions.reduce((sum, t) => sum + t.amount, 0),
        count: transactions.length
      };
    } else if (card.type === 'chart') {
      // Dados para gráfico de linha/barras por período
      const dailyData = {};

      transactions.forEach(t => {
        const date = new Date(t.date).toISOString().split('T')[0];
        if (!dailyData[date]) {
          dailyData[date] = 0;
        }
        dailyData[date] += t.amount;
      });

      data = {
        chartData: Object.entries(dailyData)
          .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB))
          .map(([date, value]) => ({
            date,
            value,
            formattedDate: new Date(date).toLocaleDateString('pt-BR', {
              day: '2-digit',
              month: '2-digit'
            })
          })),
        total: transactions.reduce((sum, t) => sum + t.amount, 0)
      };
    }

    res.json(data);
  } catch (error) {
    console.error('Erro ao buscar dados do card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
