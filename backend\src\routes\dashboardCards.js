const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter todos os cards do usuário por perfil
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { profileId } = req.query;

    let whereClause = { userId };

    if (profileId) {
      whereClause.profileId = profileId;
    } else {
      // Se não especificar perfil, buscar o perfil padrão
      const defaultProfile = await prisma.dashboardProfile.findFirst({
        where: { userId, isDefault: true }
      });

      if (defaultProfile) {
        whereClause.profileId = defaultProfile.id;
      } else {
        whereClause.profileId = null;
      }
    }

    const cards = await prisma.dashboardCard.findMany({
      where: whereClause,
      orderBy: { createdAt: 'asc' }
    });

    // Parse dos JSONs
    const parsedCards = cards.map(card => ({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    }));

    res.json(parsedCards);
  } catch (error) {
    console.error('Erro ao buscar cards:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo card
router.post('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { title, type, position, config, categories, profileId } = req.body;

    let finalProfileId = profileId;

    // Se não especificar perfil, usar o perfil padrão
    if (!finalProfileId) {
      const defaultProfile = await prisma.dashboardProfile.findFirst({
        where: { userId, isDefault: true }
      });
      finalProfileId = defaultProfile?.id;
    }

    const card = await prisma.dashboardCard.create({
      data: {
        userId,
        profileId: finalProfileId,
        title,
        type,
        position: JSON.stringify(position),
        config: JSON.stringify(config),
        categories: JSON.stringify(categories)
      }
    });

    res.json({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    });
  } catch (error) {
    console.error('Erro ao criar card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar card
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { title, type, position, config, categories, visible } = req.body;

    const card = await prisma.dashboardCard.update({
      where: {
        id,
        userId // Garantir que o usuário só pode editar seus próprios cards
      },
      data: {
        title,
        type,
        position: JSON.stringify(position),
        config: JSON.stringify(config),
        categories: JSON.stringify(categories),
        visible
      }
    });

    res.json({
      ...card,
      position: JSON.parse(card.position),
      config: JSON.parse(card.config),
      categories: JSON.parse(card.categories)
    });
  } catch (error) {
    console.error('Erro ao atualizar card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar posições de múltiplos cards
router.put('/positions/bulk', async (req, res) => {
  try {
    const userId = req.user.id;
    const { cards } = req.body;

    const updatePromises = cards.map(card =>
      prisma.dashboardCard.update({
        where: {
          id: card.id,
          userId
        },
        data: {
          position: JSON.stringify(card.position)
        }
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Posições atualizadas com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar posições:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar card
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    await prisma.dashboardCard.delete({
      where: {
        id,
        userId
      }
    });

    res.json({ message: 'Card removido com sucesso' });
  } catch (error) {
    console.error('Erro ao remover card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter dados de um card específico
router.get('/:id/data', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;

    // Buscar o card
    const card = await prisma.dashboardCard.findFirst({
      where: { id, userId }
    });

    if (!card) {
      return res.status(404).json({ error: 'Card não encontrado' });
    }

    const categories = JSON.parse(card.categories);
    const config = JSON.parse(card.config);

    // Buscar nomes das categorias
    const categoryData = await prisma.category.findMany({
      where: {
        id: { in: categories }
      },
      select: {
        id: true,
        name: true,
        icon: true
      }
    });

    const categoryNames = categoryData.map(cat => cat.name);

    // Gerar cores automáticas para cada categoria
    const categoryColors = {};
    const colorPalette = [
      '#EF4444', '#22C55E', '#3B82F6', '#F97316', '#8B5CF6',
      '#EC4899', '#EAB308', '#6366F1', '#14B8A6', '#F59E0B',
      '#DC2626', '#16A34A', '#2563EB', '#EA580C', '#7C3AED',
      '#DB2777', '#CA8A04', '#4F46E5', '#0D9488', '#D97706'
    ];

    categoryData.forEach((cat, index) => {
      categoryColors[cat.name] = colorPalette[index % colorPalette.length];
    });

    // Definir período baseado no tipo de filtro
    let startDate, endDate;

    if (config.period === 'month') {
      startDate = new Date(year, month - 1, 1);
      endDate = new Date(year, month, 0, 23, 59, 59);
    } else {
      startDate = new Date(year, 0, 1);
      endDate = new Date(year, 11, 31, 23, 59, 59);
    }

    // Buscar transações
    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        categoryId: { in: categories },
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        category: true
      },
      orderBy: { date: 'desc' }
    });

    // Calcular dados baseado no tipo do card
    let data = {};

    if (card.type === 'numeric') {
      const total = transactions.reduce((sum, t) => sum + t.amount, 0);
      const count = transactions.length;

      // Calcular período anterior para comparação
      let previousStartDate, previousEndDate;
      if (config.period === 'month') {
        // Mês anterior
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        previousStartDate = new Date(prevYear, prevMonth - 1, 1);
        previousEndDate = new Date(prevYear, prevMonth, 0, 23, 59, 59);
      } else {
        // Ano anterior
        previousStartDate = new Date(year - 1, 0, 1);
        previousEndDate = new Date(year - 1, 11, 31, 23, 59, 59);
      }

      // Buscar transações do período anterior
      const previousTransactions = await prisma.transaction.findMany({
        where: {
          userId,
          categoryId: { in: categories },
          date: {
            gte: previousStartDate,
            lte: previousEndDate
          }
        }
      });

      const previousTotal = previousTransactions.reduce((sum, t) => sum + t.amount, 0);
      const change = previousTotal > 0 ? ((total - previousTotal) / previousTotal) * 100 : 0;

      data = {
        total,
        count,
        previousTotal,
        change,
        transactions: transactions.slice(0, 10), // Últimas 10 para preview
        allTransactions: transactions,
        categoryNames
      };
    } else if (card.type === 'pie') {
      const categoryTotals = {};

      transactions.forEach(t => {
        const catName = t.category?.name || 'Sem categoria';
        if (!categoryTotals[catName]) {
          categoryTotals[catName] = {
            name: catName,
            value: 0,
            color: categoryColors[catName] || '#6B7280',
            icon: t.category?.icon || '📊'
          };
        }
        categoryTotals[catName].value += t.amount;
      });

      data = {
        chartData: Object.values(categoryTotals),
        total: transactions.reduce((sum, t) => sum + t.amount, 0),
        categoryNames,
        categoryColors
      };
    } else if (card.type === 'table') {
      data = {
        transactions: transactions.slice(0, 20),
        total: transactions.reduce((sum, t) => sum + t.amount, 0),
        count: transactions.length,
        categoryNames
      };
    } else if (card.type === 'chart') {
      // Dados para gráfico de linha por categoria
      const categoryDailyData = {};

      // Inicializar dados por categoria
      categoryData.forEach(cat => {
        categoryDailyData[cat.name] = {};
      });

      transactions.forEach(t => {
        const date = new Date(t.date).toISOString().split('T')[0];
        const categoryName = t.category?.name || 'Sem categoria';

        if (!categoryDailyData[categoryName]) {
          categoryDailyData[categoryName] = {};
        }

        if (!categoryDailyData[categoryName][date]) {
          categoryDailyData[categoryName][date] = 0;
        }
        categoryDailyData[categoryName][date] += t.amount;
      });

      // Criar array de datas únicas ordenadas
      const allDates = [...new Set(transactions.map(t =>
        new Date(t.date).toISOString().split('T')[0]
      ))].sort((a, b) => new Date(a) - new Date(b));

      // Criar dados do gráfico
      const chartData = allDates.map(date => {
        const dataPoint = {
          date,
          formattedDate: new Date(date).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit'
          })
        };

        // Adicionar valor de cada categoria
        Object.keys(categoryDailyData).forEach(categoryName => {
          dataPoint[categoryName] = categoryDailyData[categoryName][date] || 0;
        });

        return dataPoint;
      });

      data = {
        chartData,
        categoryNames,
        categoryColors,
        categories: Object.keys(categoryDailyData),
        total: transactions.reduce((sum, t) => sum + t.amount, 0)
      };
    }

    res.json(data);
  } catch (error) {
    console.error('Erro ao buscar dados do card:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
