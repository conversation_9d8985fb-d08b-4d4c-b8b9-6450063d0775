@echo off
echo ========================================
echo    🔍 Diagnóstico do Frontend
echo ========================================
echo.
echo Verificando problemas no frontend...
echo.

echo 1. Verificando se o servidor está rodando...
curl -s http://localhost:5173 > nul
if %errorlevel% equ 0 (
    echo ✅ Frontend está rodando na porta 5173
) else (
    echo ❌ Frontend não está rodando
    echo Execute: npm run dev
    pause
    exit /b 1
)

echo.
echo 2. Verificando se o backend está rodando...
curl -s http://localhost:3001/api/health > nul
if %errorlevel% equ 0 (
    echo ✅ Backend está rodando na porta 3001
) else (
    echo ❌ Backend não está rodando
    echo Execute: npm run dev no diretório backend
    pause
    exit /b 1
)

echo.
echo 3. Verificando estrutura de arquivos...
if exist "frontend\src\pages\LoansSimple.jsx" (
    echo ✅ LoansSimple.jsx existe
) else (
    echo ❌ LoansSimple.jsx não encontrado
)

if exist "frontend\src\components\Sidebar.jsx" (
    echo ✅ Sidebar.jsx existe
) else (
    echo ❌ Sidebar.jsx não encontrado
)

if exist "frontend\src\services\loanService.js" (
    echo ✅ loanService.js existe
) else (
    echo ❌ loanService.js não encontrado
)

echo.
echo 4. Verificando dependências...
cd frontend
echo Verificando node_modules...
if exist "node_modules" (
    echo ✅ node_modules existe
) else (
    echo ❌ node_modules não encontrado
    echo Execute: npm install
    pause
    exit /b 1
)

echo.
echo 5. Verificando package.json...
if exist "package.json" (
    echo ✅ package.json existe
) else (
    echo ❌ package.json não encontrado
)

cd ..

echo.
echo ========================================
echo    📋 Instruções de Correção
echo ========================================
echo.
echo Se o frontend está branco, tente:
echo.
echo 1️⃣  Abra o navegador em: http://localhost:5173
echo 2️⃣  Pressione F12 para abrir DevTools
echo 3️⃣  Vá na aba Console
echo 4️⃣  Procure por erros em vermelho
echo.
echo 🔧 Soluções comuns:
echo.
echo • Se houver erro de importação:
echo   - Verifique se todos os arquivos existem
echo   - Verifique se os nomes estão corretos
echo.
echo • Se houver erro de sintaxe:
echo   - Verifique se há vírgulas ou chaves faltando
echo   - Verifique se os imports estão corretos
echo.
echo • Se a página não carregar:
echo   - Reinicie o servidor: Ctrl+C e npm run dev
echo   - Limpe o cache: Ctrl+Shift+R
echo.
echo 🚀 Para testar a versão simplificada:
echo   Acesse: http://localhost:5173/loans
echo.
echo ⚠️  Se ainda não funcionar:
echo   1. Pare o servidor (Ctrl+C)
echo   2. Execute: npm install
echo   3. Execute: npm run dev
echo   4. Acesse novamente
echo.
pause
echo.
echo 🌐 Abrindo navegador...
start http://localhost:5173/loans
echo.
echo 📊 Abrindo DevTools automaticamente...
echo Pressione F12 no navegador para ver o console
echo.
pause
