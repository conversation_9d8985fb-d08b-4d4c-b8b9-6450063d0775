import React, { useState, useEffect } from 'react'
import { Plus, Edit3, Trash2, CreditCard } from 'lucide-react'
import { paymentMethodService } from '../services/bankService'
import toast from 'react-hot-toast'

function PaymentMethodManager() {
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingMethod, setEditingMethod] = useState(null)

  const [newMethod, setNewMethod] = useState({
    name: '',
    icon: '💳',
    color: '#3B82F6',
    type: 'CREDIT'
  })

  const methodIcons = ['💳', '💵', '📱', '🏦', '📄', '💰', '🔄', '💎']
  const methodColors = [
    '#3B82F6', '#22C55E', '#F59E0B', '#EF4444',
    '#8B5CF6', '#EC4899', '#14B8A6', '#6B7280'
  ]

  const methodTypes = [
    { value: 'CREDIT', label: 'Cartão de Crédito', icon: '💳' },
    { value: 'DEBIT', label: 'Cartão de Débito', icon: '💳' },
    { value: 'CASH', label: 'Dinheiro', icon: '💵' },
    { value: 'PIX', label: 'PIX', icon: '📱' },
    { value: 'TRANSFER', label: 'Transferência', icon: '🔄' },
    { value: 'OTHER', label: 'Outros', icon: '📄' }
  ]

  useEffect(() => {
    fetchPaymentMethods()
  }, [])

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      setPaymentMethods(data)
    } catch (error) {
      toast.error('Erro ao carregar formas de pagamento')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateMethod = async () => {
    try {
      if (!newMethod.name || !newMethod.type) {
        toast.error('Nome e tipo são obrigatórios')
        return
      }

      await paymentMethodService.createPaymentMethod(newMethod)
      toast.success('Forma de pagamento criada com sucesso!')
      setShowAddModal(false)
      setNewMethod({ name: '', icon: '💳', color: '#3B82F6', type: 'CREDIT' })
      fetchPaymentMethods()
    } catch (error) {
      toast.error('Erro ao criar forma de pagamento')
    }
  }

  const handleUpdateMethod = async () => {
    try {
      await paymentMethodService.updatePaymentMethod(editingMethod.id, editingMethod)
      toast.success('Forma de pagamento atualizada com sucesso!')
      setShowEditModal(false)
      setEditingMethod(null)
      fetchPaymentMethods()
    } catch (error) {
      toast.error('Erro ao atualizar forma de pagamento')
    }
  }

  const handleDeleteMethod = async (method) => {
    if (!confirm(`Tem certeza que deseja excluir "${method.name}"?`)) {
      return
    }

    try {
      await paymentMethodService.deletePaymentMethod(method.id)
      toast.success('Forma de pagamento excluída com sucesso!')
      fetchPaymentMethods()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao excluir forma de pagamento')
    }
  }

  const handleCreateDefaults = async () => {
    try {
      const result = await paymentMethodService.createDefaults()
      toast.success(result.message)
      fetchPaymentMethods()
    } catch (error) {
      toast.error('Erro ao criar formas de pagamento padrão')
    }
  }

  const getTypeLabel = (type) => {
    const typeObj = methodTypes.find(t => t.value === type)
    return typeObj ? typeObj.label : type
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Formas de Pagamento</h2>
          <p className="text-gray-600">Gerencie suas formas de pagamento e recebimento</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleCreateDefaults}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <CreditCard className="h-4 w-4" />
            <span>Criar Padrões</span>
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Forma</span>
          </button>
        </div>
      </div>

      {/* Lista de Formas de Pagamento */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className="bg-white rounded-xl border-2 border-gray-200 p-6 hover:border-gray-300 transition-all"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center text-xl"
                  style={{ backgroundColor: method.color + '20', color: method.color }}
                >
                  {method.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{method.name}</h3>
                  <p className="text-sm text-gray-500">{getTypeLabel(method.type)}</p>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => {
                    setEditingMethod(method)
                    setShowEditModal(true)
                  }}
                  className="p-1 hover:bg-gray-100 rounded"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteMethod(method)}
                  className="p-1 hover:bg-red-100 rounded text-red-600"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Tipo: {method.type}</span>
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: method.color }}
                title={method.color}
              />
            </div>
          </div>
        ))}
      </div>

      {paymentMethods.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma forma de pagamento cadastrada</h3>
          <p className="text-gray-600 mb-4">Comece criando suas formas de pagamento</p>
          <div className="flex justify-center space-x-3">
            <button
              onClick={handleCreateDefaults}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <CreditCard className="h-4 w-4" />
              <span>Criar Padrões</span>
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Criar Personalizada</span>
            </button>
          </div>
        </div>
      )}

      {/* Modal Adicionar Forma de Pagamento */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Nova Forma de Pagamento</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={newMethod.name}
                  onChange={(e) => setNewMethod({ ...newMethod, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Cartão Nubank"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                <select
                  value={newMethod.type}
                  onChange={(e) => setNewMethod({ ...newMethod, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {methodTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.icon} {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {methodIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setNewMethod({ ...newMethod, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        newMethod.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {methodColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setNewMethod({ ...newMethod, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        newMethod.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setNewMethod({ name: '', icon: '💳', color: '#3B82F6', type: 'CREDIT' })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateMethod}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Editar Forma de Pagamento */}
      {showEditModal && editingMethod && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Editar Forma de Pagamento</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={editingMethod.name}
                  onChange={(e) => setEditingMethod({ ...editingMethod, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                <select
                  value={editingMethod.type}
                  onChange={(e) => setEditingMethod({ ...editingMethod, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {methodTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.icon} {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {methodIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setEditingMethod({ ...editingMethod, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        editingMethod.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {methodColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setEditingMethod({ ...editingMethod, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        editingMethod.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingMethod(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateMethod}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Salvar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PaymentMethodManager
