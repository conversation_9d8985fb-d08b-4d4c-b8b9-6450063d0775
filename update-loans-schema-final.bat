@echo off
echo ========================================
echo    🔄 Atualizando Schema Final - Empréstimos
echo ========================================
echo.
echo Aplicando correções finais no banco de dados...
echo.

cd backend

echo 1. Verificando mudanças no schema...
echo    • paymentDate agora é opcional (DateTime?)
echo    • Adicionado installmentNumber (Int)
echo    • Corrigido problema de criação de parcelas
echo.

echo 2. Aplicando mudanças no banco...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    echo.
    echo Tentando reset do banco...
    call npx prisma db push --force-reset
    if %errorlevel% neq 0 (
        echo ❌ Erro crítico no banco de dados
        pause
        exit /b 1
    )
)

echo.
echo 3. Gerando cliente Prisma...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo.
echo ✅ Schema atualizado com sucesso!
echo.
echo 📋 Correções aplicadas:
echo    • ✅ paymentDate opcional corrigido
echo    • ✅ installmentNumber adicionado
echo    • ✅ Rota de deletar empréstimo criada
echo    • ✅ Botão de deletar no modal
echo    • ✅ Estatísticas em tempo real
echo    • ✅ Progresso atualizado automaticamente
echo.
echo 🎯 Funcionalidades agora disponíveis:
echo    • Criar empréstimos sem erro 500
echo    • Deletar empréstimos (remove transações)
echo    • Pagar parcelas individuais
echo    • Marcar empréstimo como quitado
echo    • Estatísticas atualizadas em tempo real
echo    • Progresso visual correto
echo.

cd ..

echo 🚀 Para testar as correções:
echo    1. Execute: start.bat
echo    2. Acesse: http://localhost:5173/loans
echo    3. Crie um empréstimo (sem erro 500)
echo    4. Veja as transações automáticas
echo    5. Teste pagamentos e estatísticas
echo    6. Teste deletar empréstimo
echo.
pause
