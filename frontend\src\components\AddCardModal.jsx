import React, { useState, useEffect } from 'react'
import { X, Plus, <PERSON><PERSON>hart3, <PERSON>Chart, Table, Hash, Wallet, PiggyBank, Calendar, CreditCard } from 'lucide-react'
import api from '../services/api'
import toast from 'react-hot-toast'

function AddCardModal({ isOpen, onClose, onCardAdded, profileId }) {
  const [step, setStep] = useState(1)
  const [cardData, setCardData] = useState({
    title: '',
    type: '',
    categories: [],
    dataSource: 'categories', // 'categories', 'banks', 'savings', 'subscriptions', 'bills'
    config: {
      color: '#3B82F6',
      period: 'year'
    }
  })
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [savings, setSavings] = useState([])
  const [subscriptions, setSubscriptions] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      fetchCategories()
      setStep(1)
      setCardData({
        title: '',
        type: '',
        categories: [],
        config: {
          color: '#3B82F6',
          period: 'year'
        }
      })
    }
  }, [isOpen])

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const cardTypes = [
    {
      type: 'numeric',
      name: 'Card Numérico',
      description: 'Exibe um valor total com contador de transações',
      icon: Hash,
      color: 'bg-blue-500'
    },
    {
      type: 'pie',
      name: 'Gráfico de Pizza',
      description: 'Mostra distribuição por categorias em formato de pizza',
      icon: PieChart,
      color: 'bg-green-500'
    },
    {
      type: 'table',
      name: 'Tabela',
      description: 'Lista as transações em formato de tabela',
      icon: Table,
      color: 'bg-purple-500'
    },
    {
      type: 'chart',
      name: 'Gráfico de Linha',
      description: 'Exibe evolução temporal dos valores',
      icon: BarChart3,
      color: 'bg-orange-500'
    }
  ]

  const dataSources = [
    {
      value: 'categories',
      name: 'Categorias',
      description: 'Baseado em categorias de transações',
      icon: '📊'
    },
    {
      value: 'banks',
      name: 'Bancos',
      description: 'Informações dos bancos e saldos',
      icon: '🏦'
    },
    {
      value: 'savings',
      name: 'Cofrinhos',
      description: 'Metas e economias',
      icon: '🐷'
    },
    {
      value: 'subscriptions',
      name: 'Assinaturas',
      description: 'Assinaturas recorrentes',
      icon: '📅'
    },
    {
      value: 'bills',
      name: 'Faturas',
      description: 'Faturas de cartões de crédito',
      icon: '💳'
    }
  ]

  const colors = [
    // Vermelhos
    '#EF4444', '#DC2626', '#B91C1C', '#991B1B',
    // Laranjas
    '#F97316', '#EA580C', '#C2410C', '#9A3412',
    // Amarelos
    '#EAB308', '#CA8A04', '#A16207', '#854D0E',
    // Verdes
    '#22C55E', '#16A34A', '#15803D', '#166534',
    // Azuis
    '#3B82F6', '#2563EB', '#1D4ED8', '#1E40AF',
    // Índigos
    '#6366F1', '#4F46E5', '#4338CA', '#3730A3',
    // Roxos
    '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6',
    // Rosas
    '#EC4899', '#DB2777', '#BE185D', '#9D174D',
    // Cinzas
    '#6B7280', '#4B5563', '#374151', '#1F2937'
  ]

  const handleCreateCard = async () => {
    if (!cardData.title || !cardData.type) {
      toast.error('Preencha todos os campos obrigatórios')
      return
    }

    if (cardData.dataSource === 'categories' && cardData.categories.length === 0) {
      toast.error('Selecione pelo menos uma categoria')
      return
    }

    try {
      setLoading(true)

      const newCard = {
        title: cardData.title,
        type: cardData.type,
        categories: cardData.categories,
        config: cardData.config,
        profileId: profileId,
        position: {
          x: Math.floor(Math.random() * 8),
          y: 0,
          w: cardData.type === 'numeric' ? 3 : 6,
          h: cardData.type === 'numeric' ? 2 : 4
        }
      }

      const response = await api.post('/dashboard-cards', newCard)
      onCardAdded(response.data)
      onClose()
      toast.success('Card adicionado com sucesso!')
    } catch (error) {
      console.error('Erro ao criar card:', error)
      toast.error('Erro ao criar card')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Adicionar Novo Card</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {step === 1 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Escolha o tipo de card
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {cardTypes.map((type) => (
                  <button
                    key={type.type}
                    onClick={() => {
                      setCardData({ ...cardData, type: type.type })
                      setStep(2)
                    }}
                    className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                      cardData.type === type.type
                        ? 'border-slate-500 bg-slate-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-2 rounded-lg ${type.color} text-white`}>
                        <type.icon className="h-5 w-5" />
                      </div>
                      <h4 className="font-semibold text-gray-900">{type.name}</h4>
                    </div>
                    <p className="text-sm text-gray-600">{type.description}</p>
                  </button>
                ))}
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Escolha a fonte de dados
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  {dataSources.map((source) => (
                    <button
                      key={source.value}
                      onClick={() => {
                        setCardData({ ...cardData, dataSource: source.value })
                        setStep(3)
                      }}
                      className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                        cardData.dataSource === source.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{source.icon}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{source.name}</h4>
                          <p className="text-sm text-gray-600">{source.description}</p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {step === 3 && cardData.dataSource === 'categories' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Selecionar Categorias
                </h3>

                {/* Título */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Título do Card *
                  </label>
                  <input
                    type="text"
                    value={cardData.title}
                    onChange={(e) => setCardData({ ...cardData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
                    placeholder="Ex: Gastos com Alimentação"
                  />
                </div>

                {/* Categorias */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Categorias *
                    </label>
                    <button
                      type="button"
                      onClick={() => {
                        const allSelected = cardData.categories.length === categories.length
                        setCardData({
                          ...cardData,
                          categories: allSelected ? [] : categories.map(cat => cat.id)
                        })
                      }}
                      className="text-xs text-slate-600 hover:text-slate-700 font-medium"
                    >
                      {cardData.categories.length === categories.length ? 'Desmarcar Todas' : 'Selecionar Todas'}
                    </button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={cardData.categories.includes(category.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setCardData({
                                ...cardData,
                                categories: [...cardData.categories, category.id]
                              })
                            } else {
                              setCardData({
                                ...cardData,
                                categories: cardData.categories.filter(id => id !== category.id)
                              })
                            }
                          }}
                          className="rounded border-gray-300 text-slate-600 focus:ring-slate-500"
                        />
                        <span className="text-sm text-gray-700">
                          {category.icon} {category.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Período */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Período
                  </label>
                  <select
                    value={cardData.config.period}
                    onChange={(e) => setCardData({
                      ...cardData,
                      config: { ...cardData.config, period: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
                  >
                    <option value="year">Ano atual</option>
                    <option value="month">Mês atual</option>
                  </select>
                </div>

                {/* Cor - apenas para cards numéricos e tabela */}
                {(cardData.type === 'numeric' || cardData.type === 'table') && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cor
                    </label>
                    <div className="grid grid-cols-8 gap-2">
                      {colors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setCardData({
                            ...cardData,
                            config: { ...cardData.config, color }
                          })}
                          className={`w-10 h-10 rounded-lg border-2 transition-all hover:scale-105 ${
                            cardData.config.color === color ? 'border-gray-800 scale-110 shadow-lg' : 'border-gray-300 hover:border-gray-500'
                          }`}
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Aviso para cards de linha e pizza */}
                {(cardData.type === 'chart' || cardData.type === 'pie') && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-700">
                      <span className="font-medium">💡 Dica:</span> As cores serão atribuídas automaticamente para cada categoria,
                      garantindo melhor diferenciação visual.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {step === 3 && cardData.dataSource !== 'categories' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Configurar Card - {dataSources.find(s => s.value === cardData.dataSource)?.name}
                </h3>

                {/* Título */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Título do Card
                  </label>
                  <input
                    type="text"
                    value={cardData.title}
                    onChange={(e) => setCardData({ ...cardData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={`Ex: Meus ${dataSources.find(s => s.value === cardData.dataSource)?.name}`}
                  />
                </div>

                {/* Período */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Período
                  </label>
                  <select
                    value={cardData.config.period}
                    onChange={(e) => setCardData({
                      ...cardData,
                      config: { ...cardData.config, period: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="month">Mensal</option>
                    <option value="year">Anual</option>
                  </select>
                </div>

                {/* Cor */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cor
                  </label>
                  <div className="grid grid-cols-8 gap-2">
                    {colors.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setCardData({
                          ...cardData,
                          config: { ...cardData.config, color }
                        })}
                        className={`w-10 h-10 rounded-lg border-2 transition-all hover:scale-105 ${
                          cardData.config.color === color ? 'border-gray-800 scale-110 shadow-lg' : 'border-gray-300 hover:border-gray-500'
                        }`}
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <span className="font-medium">ℹ️ Informação:</span> Este card exibirá dados de {dataSources.find(s => s.value === cardData.dataSource)?.name.toLowerCase()} automaticamente.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 flex justify-between">
          {(step === 2 || step === 3) && (
            <button
              onClick={() => setStep(step - 1)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Voltar
            </button>
          )}

          <div className="flex space-x-3 ml-auto">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancelar
            </button>

            {step === 3 && (
              <button
                onClick={handleCreateCard}
                disabled={loading || !cardData.title || !cardData.type || (cardData.dataSource === 'categories' && cardData.categories.length === 0)}
                className="px-4 py-2 bg-gradient-to-r from-slate-700 to-slate-800 text-white rounded-lg hover:from-slate-800 hover:to-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                <span>Criar Card</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddCardModal
