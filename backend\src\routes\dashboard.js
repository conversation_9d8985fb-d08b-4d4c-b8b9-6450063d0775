const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter dados do dashboard
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { year = new Date().getFullYear() } = req.query;

    const startOfYear = new Date(year, 0, 1);
    const endOfYear = new Date(year, 11, 31, 23, 59, 59);

    // Ano anterior para comparação
    const previousYear = parseInt(year) - 1;
    const startOfPreviousYear = new Date(previousYear, 0, 1);
    const endOfPreviousYear = new Date(previousYear, 11, 31, 23, 59, 59);

    // <PERSON>car todas as transações do ano atual
    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startOfYear,
          lte: endOfYear
        }
      },
      include: {
        category: true
      }
    });

    // Buscar transações do ano anterior para comparação
    const previousYearTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startOfPreviousYear,
          lte: endOfPreviousYear
        }
      },
      include: {
        category: true
      }
    });

    // Calcular totais do ano atual
    const totalIncome = transactions
      .filter(t => t.type === 'INCOME')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = transactions
      .filter(t => t.type === 'EXPENSE')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalInvestments = transactions
      .filter(t => t.type === 'INVESTMENT')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalLoans = transactions
      .filter(t => t.type === 'LOAN')
      .reduce((sum, t) => sum + t.amount, 0);

    // Calcular totais do ano anterior
    const previousTotalIncome = previousYearTransactions
      .filter(t => t.type === 'INCOME')
      .reduce((sum, t) => sum + t.amount, 0);

    const previousTotalExpenses = previousYearTransactions
      .filter(t => t.type === 'EXPENSE')
      .reduce((sum, t) => sum + t.amount, 0);

    const previousTotalInvestments = previousYearTransactions
      .filter(t => t.type === 'INVESTMENT')
      .reduce((sum, t) => sum + t.amount, 0);

    const previousTotalLoans = previousYearTransactions
      .filter(t => t.type === 'LOAN')
      .reduce((sum, t) => sum + t.amount, 0);

    // Calcular percentuais de mudança
    const calculatePercentageChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const incomeChange = calculatePercentageChange(totalIncome, previousTotalIncome);
    const expensesChange = calculatePercentageChange(totalExpenses, previousTotalExpenses);
    const investmentsChange = calculatePercentageChange(totalInvestments, previousTotalInvestments);
    const loansChange = calculatePercentageChange(totalLoans, previousTotalLoans);

    // Calcular médias mensais
    const monthlyExpenses = Array.from({ length: 12 }, (_, i) => {
      const monthStart = new Date(year, i, 1);
      const monthEnd = new Date(year, i + 1, 0, 23, 59, 59);

      return transactions
        .filter(t => t.type === 'EXPENSE' && t.date >= monthStart && t.date <= monthEnd)
        .reduce((sum, t) => sum + t.amount, 0);
    });

    const monthlyIncome = Array.from({ length: 12 }, (_, i) => {
      const monthStart = new Date(year, i, 1);
      const monthEnd = new Date(year, i + 1, 0, 23, 59, 59);

      return transactions
        .filter(t => t.type === 'INCOME' && t.date >= monthStart && t.date <= monthEnd)
        .reduce((sum, t) => sum + t.amount, 0);
    });

    const avgMonthlyExpenses = monthlyExpenses.reduce((sum, val) => sum + val, 0) / 12;
    const avgMonthlyIncome = monthlyIncome.reduce((sum, val) => sum + val, 0) / 12;

    // Calcular saldo líquido
    const netBalance = totalIncome - totalExpenses;
    const liquidBalance = netBalance - totalInvestments;

    // Calcular percentual de lucro líquido
    const liquidProfitPercentage = totalIncome > 0 ? (liquidBalance / totalIncome) * 100 : 0;

    // Dados para gráfico de saldo mensal
    const monthlyBalance = Array.from({ length: 12 }, (_, i) => {
      const monthStart = new Date(year, i, 1);
      const monthEnd = new Date(year, i + 1, 0, 23, 59, 59);

      const monthIncome = transactions
        .filter(t => t.type === 'INCOME' && t.date >= monthStart && t.date <= monthEnd)
        .reduce((sum, t) => sum + t.amount, 0);

      const monthExpenses = transactions
        .filter(t => t.type === 'EXPENSE' && t.date >= monthStart && t.date <= monthEnd)
        .reduce((sum, t) => sum + t.amount, 0);

      return monthIncome - monthExpenses;
    });

    // Dados para gráfico de receitas e despesas
    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: i + 1,
      income: monthlyIncome[i],
      expenses: monthlyExpenses[i]
    }));

    // Contas a pagar e receber (próximos 30 dias)
    const next30Days = new Date();
    next30Days.setDate(next30Days.getDate() + 30);

    const accountsPayable = await prisma.transaction.count({
      where: {
        userId,
        type: 'EXPENSE',
        date: {
          gte: new Date(),
          lte: next30Days
        }
      }
    });

    const accountsReceivable = await prisma.transaction.count({
      where: {
        userId,
        type: 'INCOME',
        date: {
          gte: new Date(),
          lte: next30Days
        }
      }
    });

    // Calcular estatísticas adicionais
    const netWorth = totalIncome - totalExpenses + totalInvestments;
    const savingsRate = totalIncome > 0 ? ((totalIncome - totalExpenses) / totalIncome) * 100 : 0;
    const expenseRatio = totalIncome > 0 ? (totalExpenses / totalIncome) * 100 : 0;
    const investmentRate = totalIncome > 0 ? (totalInvestments / totalIncome) * 100 : 0;

    // Calcular maior gasto e receita do ano
    const maxExpense = Math.max(...monthlyExpenses);
    const maxIncome = Math.max(...monthlyIncome);
    const minExpense = Math.min(...monthlyExpenses.filter(e => e > 0));
    const minIncome = Math.min(...monthlyIncome.filter(i => i > 0));

    res.json({
      summary: {
        totalExpenses,
        totalIncome,
        totalInvestments,
        totalLoans,
        avgMonthlyExpenses,
        avgMonthlyIncome,
        liquidBalance,
        liquidProfitPercentage,
        accountsPayable,
        accountsReceivable,
        // Novos campos
        netWorth,
        savingsRate,
        expenseRatio,
        investmentRate,
        maxExpense,
        maxIncome,
        minExpense,
        minIncome,
        // Comparações com ano anterior
        incomeChange,
        expensesChange,
        investmentsChange,
        loansChange,
        previousYear: {
          totalIncome: previousTotalIncome,
          totalExpenses: previousTotalExpenses,
          totalInvestments: previousTotalInvestments,
          totalLoans: previousTotalLoans
        }
      },
      charts: {
        monthlyBalance,
        monthlyData
      },
      year: parseInt(year)
    });
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
