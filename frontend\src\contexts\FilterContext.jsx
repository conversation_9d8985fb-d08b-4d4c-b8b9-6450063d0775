import React, { createContext, useContext, useState, useEffect } from 'react'

const FilterContext = createContext()

export const useFilters = () => {
  const context = useContext(FilterContext)
  if (!context) {
    throw new Error('useFilters must be used within a FilterProvider')
  }
  return context
}

export const FilterProvider = ({ children }) => {
  // Estado dos filtros de transações
  const [transactionFilters, setTransactionFilters] = useState(() => {
    const saved = localStorage.getItem('transactionFilters')
    return saved ? JSON.parse(saved) : {
      search: '',
      type: '',
      category: '',
      bank: '',
      dateFrom: '',
      dateTo: '',
      sortBy: 'date',
      sortOrder: 'desc'
    }
  })

  // Estado dos filtros de empréstimos
  const [loanFilters, setLoanFilters] = useState(() => {
    const saved = localStorage.getItem('loanFilters')
    return saved ? JSON.parse(saved) : {
      search: '',
      status: '',
      type: '',
      contact: ''
    }
  })

  // Salvar filtros de transações no localStorage
  useEffect(() => {
    localStorage.setItem('transactionFilters', JSON.stringify(transactionFilters))
  }, [transactionFilters])

  // Salvar filtros de empréstimos no localStorage
  useEffect(() => {
    localStorage.setItem('loanFilters', JSON.stringify(loanFilters))
  }, [loanFilters])

  // Função para atualizar filtros de transações
  const updateTransactionFilters = (newFilters) => {
    setTransactionFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  // Função para atualizar filtros de empréstimos
  const updateLoanFilters = (newFilters) => {
    setLoanFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  // Função para limpar filtros de transações
  const clearTransactionFilters = () => {
    const defaultFilters = {
      search: '',
      type: '',
      category: '',
      bank: '',
      dateFrom: '',
      dateTo: '',
      sortBy: 'date',
      sortOrder: 'desc'
    }
    setTransactionFilters(defaultFilters)
  }

  // Função para limpar filtros de empréstimos
  const clearLoanFilters = () => {
    const defaultFilters = {
      search: '',
      status: '',
      type: '',
      contact: ''
    }
    setLoanFilters(defaultFilters)
  }

  const value = {
    // Filtros de transações
    transactionFilters,
    updateTransactionFilters,
    clearTransactionFilters,
    
    // Filtros de empréstimos
    loanFilters,
    updateLoanFilters,
    clearLoanFilters
  }

  return (
    <FilterContext.Provider value={value}>
      {children}
    </FilterContext.Provider>
  )
}

export default FilterContext
