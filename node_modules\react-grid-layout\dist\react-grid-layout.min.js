!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-dom"],e):"object"==typeof exports?exports.ReactGridLayout=e(require("react"),require("react-dom")):t.ReactGridLayout=e(t.React,t.ReactDOM)}(self,((t,e)=>(()=>{var r={325:(t,e,r)=>{t.exports=r(319).default,t.exports.utils=r(872),t.exports.calculateUtils=r(337),t.exports.Responsive=r(94).default,t.exports.Responsive.utils=r(271),t.exports.WidthProvider=r(550).default},319:(t,e,r)=>{"use strict";r.d(e,{default:()=>S});var n=r(359),o=r.n(n),i=r(367),a=r(512),s=r(872),l=r(337),u=r(318),c=r(697),p=r.n(c),f=r(193),d=r(706);const h=p().arrayOf(p().oneOf(["s","w","e","n","sw","nw","se","ne"])),g=p().oneOfType([p().node,p().func]),y={className:p().string,style:p().object,width:p().number,autoSize:p().bool,cols:p().number,draggableCancel:p().string,draggableHandle:p().string,verticalCompact:function(t){t.verticalCompact},compactType:p().oneOf(["vertical","horizontal"]),layout:function(t){var e=t.layout;void 0!==e&&r(872).validateLayout(e,"layout")},margin:p().arrayOf(p().number),containerPadding:p().arrayOf(p().number),rowHeight:p().number,maxRows:p().number,isBounded:p().bool,isDraggable:p().bool,isResizable:p().bool,allowOverlap:p().bool,preventCollision:p().bool,useCSSTransforms:p().bool,transformScale:p().number,isDroppable:p().bool,resizeHandles:h,resizeHandle:g,onLayoutChange:p().func,onDragStart:p().func,onDrag:p().func,onDragStop:p().func,onResizeStart:p().func,onResize:p().func,onResizeStop:p().func,onDrop:p().func,droppingItem:p().shape({i:p().string.isRequired,w:p().number.isRequired,h:p().number.isRequired}),children:function(t,e){const r=t[e],n={};o().Children.forEach(r,(function(t){if(null!=t?.key){if(n[t.key])throw new Error('Duplicate child key "'+t.key+'" found! This will cause problems in ReactGridLayout.');n[t.key]=!0}}))},innerRef:p().any};function m(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class b extends o().Component{constructor(){super(...arguments),m(this,"state",{resizing:null,dragging:null,className:""}),m(this,"elementRef",o().createRef()),m(this,"onDragStart",((t,e)=>{let{node:r}=e;const{onDragStart:n,transformScale:o}=this.props;if(!n)return;const i={top:0,left:0},{offsetParent:a}=r;if(!a)return;const s=a.getBoundingClientRect(),u=r.getBoundingClientRect(),c=u.left/o,p=s.left/o,f=u.top/o,d=s.top/o;i.left=c-p+a.scrollLeft,i.top=f-d+a.scrollTop,this.setState({dragging:i});const{x:h,y:g}=(0,l.calcXY)(this.getPositionParams(),i.top,i.left,this.props.w,this.props.h);return n.call(this,this.props.i,h,g,{e:t,node:r,newPosition:i})})),m(this,"onDrag",((t,e,r)=>{let{node:n,deltaX:o,deltaY:i}=e;const{onDrag:a}=this.props;if(!a)return;if(!this.state.dragging)throw new Error("onDrag called before onDragStart.");let s=this.state.dragging.top+i,c=this.state.dragging.left+o;const{isBounded:p,i:f,w:d,h,containerWidth:g}=this.props,y=this.getPositionParams();if(p){const{offsetParent:t}=n;if(t){const{margin:e,rowHeight:r,containerPadding:n}=this.props,o=t.clientHeight-(0,l.calcGridItemWHPx)(h,r,e[1]);s=(0,l.clamp)(s-n[1],0,o);const i=(0,l.calcGridColWidth)(y),a=g-(0,l.calcGridItemWHPx)(d,i,e[0]);c=(0,l.clamp)(c-n[0],0,a)}}const m={top:s,left:c};r?this.setState({dragging:m}):(0,u.flushSync)((()=>{this.setState({dragging:m})}));const{x:b,y:v}=(0,l.calcXY)(y,s,c,d,h);return a.call(this,f,b,v,{e:t,node:n,newPosition:m})})),m(this,"onDragStop",((t,e)=>{let{node:r}=e;const{onDragStop:n}=this.props;if(!n)return;if(!this.state.dragging)throw new Error("onDragEnd called before onDragStart.");const{w:o,h:i,i:a}=this.props,{left:s,top:u}=this.state.dragging,c={top:u,left:s};this.setState({dragging:null});const{x:p,y:f}=(0,l.calcXY)(this.getPositionParams(),u,s,o,i);return n.call(this,a,p,f,{e:t,node:r,newPosition:c})})),m(this,"onResizeStop",((t,e,r)=>this.onResizeHandler(t,e,r,"onResizeStop"))),m(this,"onResizeStart",((t,e,r)=>this.onResizeHandler(t,e,r,"onResizeStart"))),m(this,"onResize",((t,e,r)=>this.onResizeHandler(t,e,r,"onResize")))}shouldComponentUpdate(t,e){if(this.props.children!==t.children)return!0;if(this.props.droppingPosition!==t.droppingPosition)return!0;const r=(0,l.calcGridItemPosition)(this.getPositionParams(this.props),this.props.x,this.props.y,this.props.w,this.props.h,this.state),n=(0,l.calcGridItemPosition)(this.getPositionParams(t),t.x,t.y,t.w,t.h,e);return!(0,s.fastPositionEqual)(r,n)||this.props.useCSSTransforms!==t.useCSSTransforms}componentDidMount(){this.moveDroppingItem({})}componentDidUpdate(t){this.moveDroppingItem(t)}moveDroppingItem(t){const{droppingPosition:e}=this.props;if(!e)return;const r=this.elementRef.current;if(!r)return;const n=t.droppingPosition||{left:0,top:0},{dragging:o}=this.state,i=o&&e.left!==n.left||e.top!==n.top;if(o){if(i){const t=e.left-o.left,n=e.top-o.top;this.onDrag(e.e,{node:r,deltaX:t,deltaY:n},!0)}}else this.onDragStart(e.e,{node:r,deltaX:e.left,deltaY:e.top})}getPositionParams(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;return{cols:t.cols,containerPadding:t.containerPadding,containerWidth:t.containerWidth,margin:t.margin,maxRows:t.maxRows,rowHeight:t.rowHeight}}createStyle(t){const{usePercentages:e,containerWidth:r,useCSSTransforms:n}=this.props;let o;return n?o=(0,s.setTransform)(t):(o=(0,s.setTopLeft)(t),e&&(o.left=(0,s.perc)(t.left/r),o.width=(0,s.perc)(t.width/r))),o}mixinDraggable(t,e){return o().createElement(f.DraggableCore,{disabled:!e,onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop,handle:this.props.handle,cancel:".react-resizable-handle"+(this.props.cancel?","+this.props.cancel:""),scale:this.props.transformScale,nodeRef:this.elementRef},t)}curryResizeHandler(t,e){return(r,n)=>e(r,n,t)}mixinResizable(t,e,r){const{cols:n,minW:i,minH:a,maxW:s,maxH:u,transformScale:c,resizeHandles:p,resizeHandle:f}=this.props,h=this.getPositionParams(),g=(0,l.calcGridItemPosition)(h,0,0,n,0).width,y=(0,l.calcGridItemPosition)(h,0,0,i,a),m=(0,l.calcGridItemPosition)(h,0,0,s,u),b=[y.width,y.height],v=[Math.min(m.width,g),Math.min(m.height,1/0)];return o().createElement(d.Resizable,{draggableOpts:{disabled:!r},className:r?void 0:"react-resizable-hide",width:e.width,height:e.height,minConstraints:b,maxConstraints:v,onResizeStop:this.curryResizeHandler(e,this.onResizeStop),onResizeStart:this.curryResizeHandler(e,this.onResizeStart),onResize:this.curryResizeHandler(e,this.onResize),transformScale:c,resizeHandles:p,handle:f},t)}onResizeHandler(t,e,r,n){let{node:o,size:i,handle:a}=e;const c=this.props[n];if(!c)return;const{x:p,y:f,i:d,maxH:h,minH:g,containerWidth:y}=this.props,{minW:m,maxW:b}=this.props;let v=i;o&&(v=(0,s.resizeItemInDirection)(a,r,i,y),(0,u.flushSync)((()=>{this.setState({resizing:"onResizeStop"===n?null:v})})));let{w,h:O}=(0,l.calcWH)(this.getPositionParams(),v.width,v.height,p,f,a);w=(0,l.clamp)(w,Math.max(m,1),b),O=(0,l.clamp)(O,g,h),c.call(this,d,w,O,{e:t,node:o,size:v,handle:a})}render(){const{x:t,y:e,w:r,h:n,isDraggable:i,isResizable:s,droppingPosition:u,useCSSTransforms:c}=this.props,p=(0,l.calcGridItemPosition)(this.getPositionParams(),t,e,r,n,this.state),f=o().Children.only(this.props.children);let d=o().cloneElement(f,{ref:this.elementRef,className:(0,a.Z)("react-grid-item",f.props.className,this.props.className,{static:this.props.static,resizing:Boolean(this.state.resizing),"react-draggable":i,"react-draggable-dragging":Boolean(this.state.dragging),dropping:Boolean(u),cssTransforms:c}),style:{...this.props.style,...f.props.style,...this.createStyle(p)}});return d=this.mixinResizable(d,p,s),d=this.mixinDraggable(d,i),d}}function v(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}m(b,"propTypes",{children:p().element,cols:p().number.isRequired,containerWidth:p().number.isRequired,rowHeight:p().number.isRequired,margin:p().array.isRequired,maxRows:p().number.isRequired,containerPadding:p().array.isRequired,x:p().number.isRequired,y:p().number.isRequired,w:p().number.isRequired,h:p().number.isRequired,minW:function(t,e){const r=t[e];return"number"!=typeof r?new Error("minWidth not Number"):r>t.w||r>t.maxW?new Error("minWidth larger than item width/maxWidth"):void 0},maxW:function(t,e){const r=t[e];return"number"!=typeof r?new Error("maxWidth not Number"):r<t.w||r<t.minW?new Error("maxWidth smaller than item width/minWidth"):void 0},minH:function(t,e){const r=t[e];return"number"!=typeof r?new Error("minHeight not Number"):r>t.h||r>t.maxH?new Error("minHeight larger than item height/maxHeight"):void 0},maxH:function(t,e){const r=t[e];return"number"!=typeof r?new Error("maxHeight not Number"):r<t.h||r<t.minH?new Error("maxHeight smaller than item height/minHeight"):void 0},i:p().string.isRequired,resizeHandles:h,resizeHandle:g,onDragStop:p().func,onDragStart:p().func,onDrag:p().func,onResizeStop:p().func,onResizeStart:p().func,onResize:p().func,isDraggable:p().bool.isRequired,isResizable:p().bool.isRequired,isBounded:p().bool.isRequired,static:p().bool,useCSSTransforms:p().bool.isRequired,transformScale:p().number,className:p().string,handle:p().string,cancel:p().string,droppingPosition:p().shape({e:p().object.isRequired,left:p().number.isRequired,top:p().number.isRequired})}),m(b,"defaultProps",{className:"",cancel:"",handle:"",minH:1,minW:1,maxH:1/0,maxW:1/0,transformScale:1});const w="react-grid-layout";let O=!1;try{O=/firefox/i.test(navigator.userAgent)}catch(t){}class S extends n.Component{constructor(){super(...arguments),v(this,"state",{activeDrag:null,layout:(0,s.synchronizeLayoutWithChildren)(this.props.layout,this.props.children,this.props.cols,(0,s.compactType)(this.props),this.props.allowOverlap),mounted:!1,oldDragItem:null,oldLayout:null,oldResizeItem:null,resizing:!1,droppingDOMNode:null,children:[]}),v(this,"dragEnterCounter",0),v(this,"onDragStart",((t,e,r,n)=>{let{e:o,node:i}=n;const{layout:a}=this.state,l=(0,s.getLayoutItem)(a,t);if(!l)return;const u={w:l.w,h:l.h,x:l.x,y:l.y,placeholder:!0,i:t};return this.setState({oldDragItem:(0,s.cloneLayoutItem)(l),oldLayout:a,activeDrag:u}),this.props.onDragStart(a,l,l,null,o,i)})),v(this,"onDrag",((t,e,r,n)=>{let{e:o,node:i}=n;const{oldDragItem:a}=this.state;let{layout:l}=this.state;const{cols:u,allowOverlap:c,preventCollision:p}=this.props,f=(0,s.getLayoutItem)(l,t);if(!f)return;const d={w:f.w,h:f.h,x:f.x,y:f.y,placeholder:!0,i:t};l=(0,s.moveElement)(l,f,e,r,!0,p,(0,s.compactType)(this.props),u,c),this.props.onDrag(l,a,f,d,o,i),this.setState({layout:c?l:(0,s.compact)(l,(0,s.compactType)(this.props),u),activeDrag:d})})),v(this,"onDragStop",((t,e,r,n)=>{let{e:o,node:i}=n;if(!this.state.activeDrag)return;const{oldDragItem:a}=this.state;let{layout:l}=this.state;const{cols:u,preventCollision:c,allowOverlap:p}=this.props,f=(0,s.getLayoutItem)(l,t);if(!f)return;l=(0,s.moveElement)(l,f,e,r,!0,c,(0,s.compactType)(this.props),u,p);const d=p?l:(0,s.compact)(l,(0,s.compactType)(this.props),u);this.props.onDragStop(d,a,f,null,o,i);const{oldLayout:h}=this.state;this.setState({activeDrag:null,layout:d,oldDragItem:null,oldLayout:null}),this.onLayoutMaybeChanged(d,h)})),v(this,"onResizeStart",((t,e,r,n)=>{let{e:o,node:i}=n;const{layout:a}=this.state,l=(0,s.getLayoutItem)(a,t);l&&(this.setState({oldResizeItem:(0,s.cloneLayoutItem)(l),oldLayout:this.state.layout,resizing:!0}),this.props.onResizeStart(a,l,l,null,o,i))})),v(this,"onResize",((t,e,r,n)=>{let{e:o,node:i,size:a,handle:l}=n;const{oldResizeItem:u}=this.state,{layout:c}=this.state,{cols:p,preventCollision:f,allowOverlap:d}=this.props;let h,g,y,m=!1;const[b,v]=(0,s.withLayoutItem)(c,t,(t=>{let n;return g=t.x,y=t.y,-1!==["sw","w","nw","n","ne"].indexOf(l)&&(-1!==["sw","nw","w"].indexOf(l)&&(g=t.x+(t.w-e),e=t.x!==g&&g<0?t.w:e,g=g<0?0:g),-1!==["ne","n","nw"].indexOf(l)&&(y=t.y+(t.h-r),r=t.y!==y&&y<0?t.h:r,y=y<0?0:y),m=!0),f&&!d&&(n=(0,s.getAllCollisions)(c,{...t,w:e,h:r,x:g,y}).filter((e=>e.i!==t.i)).length>0,n&&(y=t.y,r=t.h,g=t.x,e=t.w,m=!1)),t.w=e,t.h=r,t}));if(!v)return;if(h=b,m){const t=!0;h=(0,s.moveElement)(b,v,g,y,t,this.props.preventCollision,(0,s.compactType)(this.props),p,d)}const w={w:v.w,h:v.h,x:v.x,y:v.y,static:!0,i:t};this.props.onResize(h,u,v,w,o,i),this.setState({layout:d?h:(0,s.compact)(h,(0,s.compactType)(this.props),p),activeDrag:w})})),v(this,"onResizeStop",((t,e,r,n)=>{let{e:o,node:i}=n;const{layout:a,oldResizeItem:l}=this.state,{cols:u,allowOverlap:c}=this.props,p=(0,s.getLayoutItem)(a,t),f=c?a:(0,s.compact)(a,(0,s.compactType)(this.props),u);this.props.onResizeStop(f,l,p,null,o,i);const{oldLayout:d}=this.state;this.setState({activeDrag:null,layout:f,oldResizeItem:null,oldLayout:null,resizing:!1}),this.onLayoutMaybeChanged(f,d)})),v(this,"onDragOver",(t=>{if(t.preventDefault(),t.stopPropagation(),O&&!t.nativeEvent.target?.classList.contains(w))return!1;const{droppingItem:e,onDropDragOver:r,margin:o,cols:i,rowHeight:a,maxRows:s,width:u,containerPadding:c,transformScale:p}=this.props,f=r?.(t);if(!1===f)return this.state.droppingDOMNode&&this.removeDroppingPlaceholder(),!1;const d={...e,...f},{layout:h}=this.state,g=t.currentTarget.getBoundingClientRect(),y=t.clientX-g.left,m=t.clientY-g.top,b={left:y/p,top:m/p,e:t};if(this.state.droppingDOMNode){if(this.state.droppingPosition){const{left:t,top:e}=this.state.droppingPosition;(t!=y||e!=m)&&this.setState({droppingPosition:b})}}else{const t={cols:i,margin:o,maxRows:s,rowHeight:a,containerWidth:u,containerPadding:c||o},e=(0,l.calcXY)(t,m,y,d.w,d.h);this.setState({droppingDOMNode:n.createElement("div",{key:d.i}),droppingPosition:b,layout:[...h,{...d,x:e.x,y:e.y,static:!1,isDraggable:!0}]})}})),v(this,"removeDroppingPlaceholder",(()=>{const{droppingItem:t,cols:e}=this.props,{layout:r}=this.state,n=(0,s.compact)(r.filter((e=>e.i!==t.i)),(0,s.compactType)(this.props),e,this.props.allowOverlap);this.setState({layout:n,droppingDOMNode:null,activeDrag:null,droppingPosition:void 0})})),v(this,"onDragLeave",(t=>{t.preventDefault(),t.stopPropagation(),this.dragEnterCounter--,0===this.dragEnterCounter&&this.removeDroppingPlaceholder()})),v(this,"onDragEnter",(t=>{t.preventDefault(),t.stopPropagation(),this.dragEnterCounter++})),v(this,"onDrop",(t=>{t.preventDefault(),t.stopPropagation();const{droppingItem:e}=this.props,{layout:r}=this.state,n=r.find((t=>t.i===e.i));this.dragEnterCounter=0,this.removeDroppingPlaceholder(),this.props.onDrop(r,n,t)}))}componentDidMount(){this.setState({mounted:!0}),this.onLayoutMaybeChanged(this.state.layout,this.props.layout)}static getDerivedStateFromProps(t,e){let r;return e.activeDrag?null:((0,i.deepEqual)(t.layout,e.propsLayout)&&t.compactType===e.compactType?(0,s.childrenEqual)(t.children,e.children)||(r=e.layout):r=t.layout,r?{layout:(0,s.synchronizeLayoutWithChildren)(r,t.children,t.cols,(0,s.compactType)(t),t.allowOverlap),compactType:t.compactType,children:t.children,propsLayout:t.layout}:null)}shouldComponentUpdate(t,e){return this.props.children!==t.children||!(0,s.fastRGLPropsEqual)(this.props,t,i.deepEqual)||this.state.activeDrag!==e.activeDrag||this.state.mounted!==e.mounted||this.state.droppingPosition!==e.droppingPosition}componentDidUpdate(t,e){if(!this.state.activeDrag){const t=this.state.layout,r=e.layout;this.onLayoutMaybeChanged(t,r)}}containerHeight(){if(!this.props.autoSize)return;const t=(0,s.bottom)(this.state.layout),e=this.props.containerPadding?this.props.containerPadding[1]:this.props.margin[1];return t*this.props.rowHeight+(t-1)*this.props.margin[1]+2*e+"px"}onLayoutMaybeChanged(t,e){e||(e=this.state.layout),(0,i.deepEqual)(e,t)||this.props.onLayoutChange(t)}placeholder(){const{activeDrag:t}=this.state;if(!t)return null;const{width:e,cols:r,margin:o,containerPadding:i,rowHeight:a,maxRows:s,useCSSTransforms:l,transformScale:u}=this.props;return n.createElement(b,{w:t.w,h:t.h,x:t.x,y:t.y,i:t.i,className:"react-grid-placeholder "+(this.state.resizing?"placeholder-resizing":""),containerWidth:e,cols:r,margin:o,containerPadding:i||o,maxRows:s,rowHeight:a,isDraggable:!1,isResizable:!1,isBounded:!1,useCSSTransforms:l,transformScale:u},n.createElement("div",null))}processGridItem(t,e){if(!t||!t.key)return;const r=(0,s.getLayoutItem)(this.state.layout,String(t.key));if(!r)return null;const{width:o,cols:i,margin:a,containerPadding:l,rowHeight:u,maxRows:c,isDraggable:p,isResizable:f,isBounded:d,useCSSTransforms:h,transformScale:g,draggableCancel:y,draggableHandle:m,resizeHandles:v,resizeHandle:w}=this.props,{mounted:O,droppingPosition:S}=this.state,x="boolean"==typeof r.isDraggable?r.isDraggable:!r.static&&p,D="boolean"==typeof r.isResizable?r.isResizable:!r.static&&f,P=r.resizeHandles||v,R=x&&d&&!1!==r.isBounded;return n.createElement(b,{containerWidth:o,cols:i,margin:a,containerPadding:l||a,maxRows:c,rowHeight:u,cancel:y,handle:m,onDragStop:this.onDragStop,onDragStart:this.onDragStart,onDrag:this.onDrag,onResizeStart:this.onResizeStart,onResize:this.onResize,onResizeStop:this.onResizeStop,isDraggable:x,isResizable:D,isBounded:R,useCSSTransforms:h&&O,usePercentages:!O,transformScale:g,w:r.w,h:r.h,x:r.x,y:r.y,i:r.i,minH:r.minH,minW:r.minW,maxH:r.maxH,maxW:r.maxW,static:r.static,droppingPosition:e?S:void 0,resizeHandles:P,resizeHandle:w},t)}render(){const{className:t,style:e,isDroppable:r,innerRef:o}=this.props,i=(0,a.Z)(w,t),l={height:this.containerHeight(),...e};return n.createElement("div",{ref:o,className:i,style:l,onDrop:r?this.onDrop:s.noop,onDragLeave:r?this.onDragLeave:s.noop,onDragEnter:r?this.onDragEnter:s.noop,onDragOver:r?this.onDragOver:s.noop},n.Children.map(this.props.children,(t=>this.processGridItem(t))),r&&this.state.droppingDOMNode&&this.processGridItem(this.state.droppingDOMNode,!0),this.placeholder())}}v(S,"displayName","ReactGridLayout"),v(S,"propTypes",y),v(S,"defaultProps",{autoSize:!0,cols:12,className:"",style:{},draggableHandle:"",draggableCancel:"",containerPadding:null,rowHeight:150,maxRows:1/0,layout:[],margin:[10,10],isBounded:!1,isDraggable:!0,isResizable:!0,allowOverlap:!1,isDroppable:!1,useCSSTransforms:!0,transformScale:1,verticalCompact:!0,compactType:"vertical",preventCollision:!1,droppingItem:{i:"__dropping-elem__",h:1,w:1},resizeHandles:["se"],onLayoutChange:s.noop,onDragStart:s.noop,onDrag:s.noop,onDragStop:s.noop,onResizeStart:s.noop,onResize:s.noop,onResizeStop:s.noop,onDrop:s.noop,onDropDragOver:s.noop})},94:(t,e,r)=>{"use strict";r.d(e,{default:()=>h});var n=r(359),o=r(697),i=r.n(o),a=r(367),s=r(872),l=r(271),u=r(319);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}const f=t=>Object.prototype.toString.call(t);function d(t,e){return null==t?null:Array.isArray(t)?t:t[e]}class h extends n.Component{constructor(){super(...arguments),p(this,"state",this.generateInitialState()),p(this,"onLayoutChange",(t=>{this.props.onLayoutChange(t,{...this.props.layouts,[this.state.breakpoint]:t})}))}generateInitialState(){const{width:t,breakpoints:e,layouts:r,cols:n}=this.props,o=(0,l.getBreakpointFromWidth)(e,t),i=(0,l.getColsFromBreakpoint)(o,n),a=!1===this.props.verticalCompact?null:this.props.compactType;return{layout:(0,l.findOrGenerateResponsiveLayout)(r,e,o,o,i,a),breakpoint:o,cols:i}}static getDerivedStateFromProps(t,e){if(!(0,a.deepEqual)(t.layouts,e.layouts)){const{breakpoint:r,cols:n}=e;return{layout:(0,l.findOrGenerateResponsiveLayout)(t.layouts,t.breakpoints,r,r,n,t.compactType),layouts:t.layouts}}return null}componentDidUpdate(t){this.props.width==t.width&&this.props.breakpoint===t.breakpoint&&(0,a.deepEqual)(this.props.breakpoints,t.breakpoints)&&(0,a.deepEqual)(this.props.cols,t.cols)||this.onWidthChange(t)}onWidthChange(t){const{breakpoints:e,cols:r,layouts:n,compactType:o}=this.props,i=this.props.breakpoint||(0,l.getBreakpointFromWidth)(this.props.breakpoints,this.props.width),a=this.state.breakpoint,u=(0,l.getColsFromBreakpoint)(i,r),c={...n};if(a!==i||t.breakpoints!==e||t.cols!==r){a in c||(c[a]=(0,s.cloneLayout)(this.state.layout));let t=(0,l.findOrGenerateResponsiveLayout)(c,e,i,a,u,o);t=(0,s.synchronizeLayoutWithChildren)(t,this.props.children,u,o,this.props.allowOverlap),c[i]=t,this.props.onBreakpointChange(i,u),this.props.onLayoutChange(t,c),this.setState({breakpoint:i,layout:t,cols:u})}const p=d(this.props.margin,i),f=d(this.props.containerPadding,i);this.props.onWidthChange(this.props.width,p,u,f)}render(){const{breakpoint:t,breakpoints:e,cols:r,layouts:o,margin:i,containerPadding:a,onBreakpointChange:s,onLayoutChange:l,onWidthChange:p,...f}=this.props;return n.createElement(u.default,c({},f,{margin:d(i,this.state.breakpoint),containerPadding:d(a,this.state.breakpoint),onLayoutChange:this.onLayoutChange,layout:this.state.layout,cols:this.state.cols}))}}p(h,"propTypes",{breakpoint:i().string,breakpoints:i().object,allowOverlap:i().bool,cols:i().object,margin:i().oneOfType([i().array,i().object]),containerPadding:i().oneOfType([i().array,i().object]),layouts(t,e){if("[object Object]"!==f(t[e]))throw new Error("Layout property must be an object. Received: "+f(t[e]));Object.keys(t[e]).forEach((e=>{if(!(e in t.breakpoints))throw new Error("Each key in layouts must align with a key in breakpoints.");(0,s.validateLayout)(t.layouts[e],"layouts."+e)}))},width:i().number.isRequired,onBreakpointChange:i().func,onLayoutChange:i().func,onWidthChange:i().func}),p(h,"defaultProps",{breakpoints:{lg:1200,md:996,sm:768,xs:480,xxs:0},cols:{lg:12,md:10,sm:6,xs:4,xxs:2},containerPadding:{lg:null,md:null,sm:null,xs:null,xxs:null},layouts:{},margin:[10,10],allowOverlap:!1,onBreakpointChange:s.noop,onLayoutChange:s.noop,onWidthChange:s.noop})},337:(t,e,r)=>{"use strict";function n(t){const{margin:e,containerPadding:r,containerWidth:n,cols:o}=t;return(n-e[0]*(o-1)-2*r[0])/o}function o(t,e,r){return Number.isFinite(t)?Math.round(e*t+Math.max(0,t-1)*r):t}function i(t,e,r,i,a,s){const{margin:l,containerPadding:u,rowHeight:c}=t,p=n(t),f={};return s&&s.resizing?(f.width=Math.round(s.resizing.width),f.height=Math.round(s.resizing.height)):(f.width=o(i,p,l[0]),f.height=o(a,c,l[1])),s&&s.dragging?(f.top=Math.round(s.dragging.top),f.left=Math.round(s.dragging.left)):s&&s.resizing&&"number"==typeof s.resizing.top&&"number"==typeof s.resizing.left?(f.top=Math.round(s.resizing.top),f.left=Math.round(s.resizing.left)):(f.top=Math.round((c+l[1])*r+u[1]),f.left=Math.round((p+l[0])*e+u[0])),f}function a(t,e,r,o,i){const{margin:a,containerPadding:s,cols:u,rowHeight:c,maxRows:p}=t,f=n(t);let d=Math.round((r-s[0])/(f+a[0])),h=Math.round((e-s[1])/(c+a[1]));return d=l(d,0,u-o),h=l(h,0,p-i),{x:d,y:h}}function s(t,e,r,o,i,a){const{margin:s,maxRows:u,cols:c,rowHeight:p}=t,f=n(t);let d=Math.round((e+s[0])/(f+s[0])),h=Math.round((r+s[1])/(p+s[1])),g=l(d,0,c-o),y=l(h,0,u-i);return-1!==["sw","w","nw"].indexOf(a)&&(g=l(d,0,c)),-1!==["nw","n","ne"].indexOf(a)&&(y=l(h,0,u)),{w:g,h:y}}function l(t,e,r){return Math.max(Math.min(t,r),e)}r.r(e),r.d(e,{calcGridColWidth:()=>n,calcGridItemPosition:()=>i,calcGridItemWHPx:()=>o,calcWH:()=>s,calcXY:()=>a,clamp:()=>l})},550:(t,e,r)=>{"use strict";r.d(e,{default:()=>_});var n=r(359),o=r(697),i=r.n(o),a=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var r=-1;return t.some((function(t,n){return t[0]===e&&(r=n,!0)})),r}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var r=t(this.__entries__,e),n=this.__entries__[r];return n&&n[1]},e.prototype.set=function(e,r){var n=t(this.__entries__,e);~n?this.__entries__[n][1]=r:this.__entries__.push([e,r])},e.prototype.delete=function(e){var r=this.__entries__,n=t(r,e);~n&&r.splice(n,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var r=0,n=this.__entries__;r<n.length;r++){var o=n[r];t.call(e,o[1],o[0])}},e}()}(),s="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,l=void 0!==r.g&&r.g.Math===Math?r.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),u="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(l):function(t){return setTimeout((function(){return t(Date.now())}),1e3/60)},c=["top","right","bottom","left","width","height","size","weight"],p="undefined"!=typeof MutationObserver,f=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t,e){var r=!1,n=!1,o=0;function i(){r&&(r=!1,t()),n&&s()}function a(){u(i)}function s(){var t=Date.now();if(r){if(t-o<2)return;n=!0}else r=!0,n=!1,setTimeout(a,e);o=t}return s}(this.refresh.bind(this),20)}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,r=e.indexOf(t);~r&&e.splice(r,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter((function(t){return t.gatherActive(),t.hasActive()}));return t.forEach((function(t){return t.broadcastActive()})),t.length>0},t.prototype.connect_=function(){s&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),p?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){s&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,r=void 0===e?"":e;c.some((function(t){return!!~r.indexOf(t)}))&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),d=function(t,e){for(var r=0,n=Object.keys(e);r<n.length;r++){var o=n[r];Object.defineProperty(t,o,{value:e[o],enumerable:!1,writable:!1,configurable:!0})}return t},h=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||l},g=w(0,0,0,0);function y(t){return parseFloat(t)||0}function m(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return e.reduce((function(e,r){return e+y(t["border-"+r+"-width"])}),0)}var b="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof h(t).SVGGraphicsElement}:function(t){return t instanceof h(t).SVGElement&&"function"==typeof t.getBBox};function v(t){return s?b(t)?function(t){var e=t.getBBox();return w(0,0,e.width,e.height)}(t):function(t){var e=t.clientWidth,r=t.clientHeight;if(!e&&!r)return g;var n=h(t).getComputedStyle(t),o=function(t){for(var e={},r=0,n=["top","right","bottom","left"];r<n.length;r++){var o=n[r],i=t["padding-"+o];e[o]=y(i)}return e}(n),i=o.left+o.right,a=o.top+o.bottom,s=y(n.width),l=y(n.height);if("border-box"===n.boxSizing&&(Math.round(s+i)!==e&&(s-=m(n,"left","right")+i),Math.round(l+a)!==r&&(l-=m(n,"top","bottom")+a)),!function(t){return t===h(t).document.documentElement}(t)){var u=Math.round(s+i)-e,c=Math.round(l+a)-r;1!==Math.abs(u)&&(s-=u),1!==Math.abs(c)&&(l-=c)}return w(o.left,o.top,s,l)}(t):g}function w(t,e,r,n){return{x:t,y:e,width:r,height:n}}var O=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=w(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=v(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),S=function(t,e){var r,n,o,i,a,s,l,u=(n=(r=e).x,o=r.y,i=r.width,a=r.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,l=Object.create(s.prototype),d(l,{x:n,y:o,width:i,height:a,top:o,right:n+i,bottom:a+o,left:n}),l);d(this,{target:t,contentRect:u})},x=function(){function t(t,e,r){if(this.activeObservations_=[],this.observations_=new a,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=r}return t.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new O(t)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach((function(e){e.isActive()&&t.activeObservations_.push(e)}))},t.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map((function(t){return new S(t.target,t.broadcastRect())}));this.callback_.call(t,e,t),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),D="undefined"!=typeof WeakMap?new WeakMap:new a,P=function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var r=f.getInstance(),n=new x(e,r,this);D.set(this,n)};["observe","unobserve","disconnect"].forEach((function(t){P.prototype[t]=function(){var e;return(e=D.get(this))[t].apply(e,arguments)}}));const R=void 0!==l.ResizeObserver?l.ResizeObserver:P;var j=r(512);function E(){return E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},E.apply(this,arguments)}function z(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}const C="react-grid-layout";function _(t){var e;return e=class extends n.Component{constructor(){super(...arguments),z(this,"state",{width:1280}),z(this,"elementRef",n.createRef()),z(this,"mounted",!1),z(this,"resizeObserver",void 0)}componentDidMount(){this.mounted=!0,this.resizeObserver=new R((t=>{if(this.elementRef.current instanceof HTMLElement){const e=t[0].contentRect.width;this.setState({width:e})}}));const t=this.elementRef.current;t instanceof HTMLElement&&this.resizeObserver.observe(t)}componentWillUnmount(){this.mounted=!1;const t=this.elementRef.current;t instanceof HTMLElement&&this.resizeObserver.unobserve(t),this.resizeObserver.disconnect()}render(){const{measureBeforeMount:e,...r}=this.props;return e&&!this.mounted?n.createElement("div",{className:(0,j.Z)(this.props.className,C),style:this.props.style,ref:this.elementRef}):n.createElement(t,E({innerRef:this.elementRef},r,this.state))}},z(e,"defaultProps",{measureBeforeMount:!1}),z(e,"propTypes",{measureBeforeMount:i().bool}),e}},378:t=>{t.exports=function(t,e,r){return t===e||t.className===e.className&&r(t.style,e.style)&&t.width===e.width&&t.autoSize===e.autoSize&&t.cols===e.cols&&t.draggableCancel===e.draggableCancel&&t.draggableHandle===e.draggableHandle&&r(t.verticalCompact,e.verticalCompact)&&r(t.compactType,e.compactType)&&r(t.layout,e.layout)&&r(t.margin,e.margin)&&r(t.containerPadding,e.containerPadding)&&t.rowHeight===e.rowHeight&&t.maxRows===e.maxRows&&t.isBounded===e.isBounded&&t.isDraggable===e.isDraggable&&t.isResizable===e.isResizable&&t.allowOverlap===e.allowOverlap&&t.preventCollision===e.preventCollision&&t.useCSSTransforms===e.useCSSTransforms&&t.transformScale===e.transformScale&&t.isDroppable===e.isDroppable&&r(t.resizeHandles,e.resizeHandles)&&r(t.resizeHandle,e.resizeHandle)&&t.onLayoutChange===e.onLayoutChange&&t.onDragStart===e.onDragStart&&t.onDrag===e.onDrag&&t.onDragStop===e.onDragStop&&t.onResizeStart===e.onResizeStart&&t.onResize===e.onResize&&t.onResizeStop===e.onResizeStop&&t.onDrop===e.onDrop&&r(t.droppingItem,e.droppingItem)&&r(t.innerRef,e.innerRef)}},271:(t,e,r)=>{"use strict";r.r(e),r.d(e,{findOrGenerateResponsiveLayout:()=>a,getBreakpointFromWidth:()=>o,getColsFromBreakpoint:()=>i,sortBreakpoints:()=>s});var n=r(872);function o(t,e){const r=s(t);let n=r[0];for(let o=1,i=r.length;o<i;o++){const i=r[o];e>t[i]&&(n=i)}return n}function i(t,e){if(!e[t])throw new Error("ResponsiveReactGridLayout: `cols` entry for breakpoint "+t+" is missing!");return e[t]}function a(t,e,r,o,i,a){if(t[r])return(0,n.cloneLayout)(t[r]);let l=t[o];const u=s(e),c=u.slice(u.indexOf(r));for(let e=0,r=c.length;e<r;e++){const r=c[e];if(t[r]){l=t[r];break}}return l=(0,n.cloneLayout)(l||[]),(0,n.compact)((0,n.correctBounds)(l,{cols:i}),a,i)}function s(t){return Object.keys(t).sort((function(e,r){return t[e]-t[r]}))}},872:(t,e,r)=>{"use strict";r.r(e),r.d(e,{bottom:()=>l,childrenEqual:()=>d,cloneLayout:()=>u,cloneLayoutItem:()=>f,collides:()=>y,compact:()=>m,compactItem:()=>w,compactType:()=>$,correctBounds:()=>O,fastPositionEqual:()=>g,fastRGLPropsEqual:()=>h,getAllCollisions:()=>D,getFirstCollision:()=>x,getLayoutItem:()=>S,getStatics:()=>P,modifyLayout:()=>c,moveElement:()=>R,moveElementAwayFromCollision:()=>j,noop:()=>F,perc:()=>E,resizeItemInDirection:()=>W,setTopLeft:()=>I,setTransform:()=>A,sortLayoutItems:()=>q,sortLayoutItemsByColRow:()=>G,sortLayoutItemsByRowCol:()=>B,synchronizeLayoutWithChildren:()=>X,validateLayout:()=>Y,withLayoutItem:()=>p});var n=r(367),o=r(359),i=r.n(o);const a=!0,s=!1;function l(t){let e,r=0;for(let n=0,o=t.length;n<o;n++)e=t[n].y+t[n].h,e>r&&(r=e);return r}function u(t){const e=Array(t.length);for(let r=0,n=t.length;r<n;r++)e[r]=f(t[r]);return e}function c(t,e){const r=Array(t.length);for(let n=0,o=t.length;n<o;n++)e.i===t[n].i?r[n]=e:r[n]=t[n];return r}function p(t,e,r){let n=S(t,e);return n?(n=r(f(n)),[t=c(t,n),n]):[t,null]}function f(t){return{w:t.w,h:t.h,x:t.x,y:t.y,i:t.i,minW:t.minW,maxW:t.maxW,minH:t.minH,maxH:t.maxH,moved:Boolean(t.moved),static:Boolean(t.static),isDraggable:t.isDraggable,isResizable:t.isResizable,resizeHandles:t.resizeHandles,isBounded:t.isBounded}}function d(t,e){return(0,n.deepEqual)(i().Children.map(t,(t=>t?.key)),i().Children.map(e,(t=>t?.key)))&&(0,n.deepEqual)(i().Children.map(t,(t=>t?.props["data-grid"])),i().Children.map(e,(t=>t?.props["data-grid"])))}const h=r(378);function g(t,e){return t.left===e.left&&t.top===e.top&&t.width===e.width&&t.height===e.height}function y(t,e){return!(t.i===e.i||t.x+t.w<=e.x||t.x>=e.x+e.w||t.y+t.h<=e.y||t.y>=e.y+e.h)}function m(t,e,r,n){const o=P(t),i=q(t,e),a=Array(t.length);for(let s=0,l=i.length;s<l;s++){let l=f(i[s]);l.static||(l=w(o,l,e,r,i,n),o.push(l)),a[t.indexOf(i[s])]=l,l.moved=!1}return a}const b={x:"w",y:"h"};function v(t,e,r,n){const o=b[n];e[n]+=1;for(let i=t.map((t=>t.i)).indexOf(e.i)+1;i<t.length;i++){const a=t[i];if(!a.static){if(a.y>e.y+e.h)break;y(e,a)&&v(t,a,r+e[o],n)}}e[n]=r}function w(t,e,r,n,o,i){const a="horizontal"===r;if("vertical"===r)for(e.y=Math.min(l(t),e.y);e.y>0&&!x(t,e);)e.y--;else if(a)for(;e.x>0&&!x(t,e);)e.x--;let s;for(;(s=x(t,e))&&(null!==r||!i);)if(a?v(o,e,s.x+s.w,"x"):v(o,e,s.y+s.h,"y"),a&&e.x+e.w>n)for(e.x=n-e.w,e.y++;e.x>0&&!x(t,e);)e.x--;return e.y=Math.max(e.y,0),e.x=Math.max(e.x,0),e}function O(t,e){const r=P(t);for(let n=0,o=t.length;n<o;n++){const o=t[n];if(o.x+o.w>e.cols&&(o.x=e.cols-o.w),o.x<0&&(o.x=0,o.w=e.cols),o.static)for(;x(r,o);)o.y++;else r.push(o)}return t}function S(t,e){for(let r=0,n=t.length;r<n;r++)if(t[r].i===e)return t[r]}function x(t,e){for(let r=0,n=t.length;r<n;r++)if(y(t[r],e))return t[r]}function D(t,e){return t.filter((t=>y(t,e)))}function P(t){return t.filter((t=>t.static))}function R(t,e,r,n,o,i,a,s,l){if(e.static&&!0!==e.isDraggable)return t;if(e.y===n&&e.x===r)return t;U(`Moving element ${e.i} to [${String(r)},${String(n)}] from [${e.x},${e.y}]`);const c=e.x,p=e.y;"number"==typeof r&&(e.x=r),"number"==typeof n&&(e.y=n),e.moved=!0;let f=q(t,a);("vertical"===a&&"number"==typeof n?p>=n:"horizontal"===a&&"number"==typeof r&&c>=r)&&(f=f.reverse());const d=D(f,e),h=d.length>0;if(h&&l)return u(t);if(h&&i)return U(`Collision prevented on ${e.i}, reverting.`),e.x=c,e.y=p,e.moved=!1,t;for(let r=0,n=d.length;r<n;r++){const n=d[r];U(`Resolving collision between ${e.i} at [${e.x},${e.y}] and ${n.i} at [${n.x},${n.y}]`),n.moved||(t=n.static?j(t,n,e,o,a,s):j(t,e,n,o,a,s))}return t}function j(t,e,r,n,o,i){const a="horizontal"===o,s="vertical"===o,l=e.static;if(n){n=!1;const u={x:a?Math.max(e.x-r.w,0):r.x,y:s?Math.max(e.y-r.h,0):r.y,w:r.w,h:r.h,i:"-1"},c=x(t,u),p=c&&c.y+c.h>e.y,f=c&&e.x+e.w>c.x;if(!c)return U(`Doing reverse collision on ${r.i} up to [${u.x},${u.y}].`),R(t,r,a?u.x:void 0,s?u.y:void 0,n,l,o,i);if(p&&s)return R(t,r,void 0,e.y+1,n,l,o,i);if(p&&null==o)return e.y=r.y,r.y=r.y+r.h,t;if(f&&a)return R(t,e,r.x,void 0,n,l,o,i)}const u=a?r.x+1:void 0,c=s?r.y+1:void 0;return null==u&&null==c?t:R(t,r,a?r.x+1:void 0,s?r.y+1:void 0,n,l,o,i)}function E(t){return 100*t+"%"}const z=(t,e,r,n)=>t+r>n?e:r,C=(t,e,r)=>t<0?e:r,_=t=>Math.max(0,t),M=t=>Math.max(0,t),T=(t,e,r)=>{let{left:n,height:o,width:i}=e;const a=t.top-(o-t.height);return{left:n,width:i,height:C(a,t.height,o),top:M(a)}},k=(t,e,r)=>{let{top:n,left:o,height:i,width:a}=e;return{top:n,height:i,width:z(t.left,t.width,a,r),left:_(o)}},H=(t,e,r)=>{let{top:n,height:o,width:i}=e;const a=t.left-(i-t.width);return{height:o,width:a<0?t.width:z(t.left,t.width,i,r),top:M(n),left:_(a)}},L=(t,e,r)=>{let{top:n,left:o,height:i,width:a}=e;return{width:a,left:o,height:C(n,t.height,i),top:M(n)}},N={n:T,ne:function(){return T(arguments.length<=0?void 0:arguments[0],k(...arguments))},e:k,se:function(){return L(arguments.length<=0?void 0:arguments[0],k(...arguments))},s:L,sw:function(){return L(arguments.length<=0?void 0:arguments[0],H(...arguments))},w:H,nw:function(){return T(arguments.length<=0?void 0:arguments[0],H(...arguments))}};function W(t,e,r,n){const o=N[t];return o?o(e,{...e,...r},n):r}function A(t){let{top:e,left:r,width:n,height:o}=t;const i=`translate(${r}px,${e}px)`;return{transform:i,WebkitTransform:i,MozTransform:i,msTransform:i,OTransform:i,width:`${n}px`,height:`${o}px`,position:"absolute"}}function I(t){let{top:e,left:r,width:n,height:o}=t;return{top:`${e}px`,left:`${r}px`,width:`${n}px`,height:`${o}px`,position:"absolute"}}function q(t,e){return"horizontal"===e?G(t):"vertical"===e?B(t):t}function B(t){return t.slice(0).sort((function(t,e){return t.y>e.y||t.y===e.y&&t.x>e.x?1:t.y===e.y&&t.x===e.x?0:-1}))}function G(t){return t.slice(0).sort((function(t,e){return t.x>e.x||t.x===e.x&&t.y>e.y?1:-1}))}function X(t,e,r,n,o){t=t||[];const s=[];i().Children.forEach(e,(e=>{if(null==e?.key)return;const r=S(t,String(e.key)),n=e.props["data-grid"];r&&null==n?s.push(f(r)):n?(a||Y([n],"ReactGridLayout.children"),s.push(f({...n,i:e.key}))):s.push(f({w:1,h:1,x:0,y:l(s),i:String(e.key)}))}));const u=O(s,{cols:r});return o?u:m(u,n,r)}function Y(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Layout";const r=["x","y","w","h"];if(!Array.isArray(t))throw new Error(e+" must be an array!");for(let n=0,o=t.length;n<o;n++){const o=t[n];for(let t=0;t<r.length;t++){const i=r[t],a=o[i];if("number"!=typeof a||Number.isNaN(a))throw new Error(`ReactGridLayout: ${e}[${n}].${i} must be a number! Received: ${a} (${typeof a})`)}if(void 0!==o.i&&"string"!=typeof o.i)throw new Error(`ReactGridLayout: ${e}[${n}].i must be a string! Received: ${o.i} (${typeof o.i})`)}}function $(t){const{verticalCompact:e,compactType:r}=t||{};return!1===e?null:r}function U(){s&&console.log(...arguments)}const F=()=>{}},367:function(t,e){!function(t){"use strict";function e(t){return function(e,r,n,o,i,a,s){return t(e,r,s)}}function r(t){return function(e,r,n,o){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n,o);var i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var s=t(e,r,n,o);return o.delete(e),o.delete(r),s}}function n(t,e){var r={};for(var n in t)r[n]=t[n];for(var n in e)r[n]=e[n];return r}function o(t){return t.constructor===Object||null==t.constructor}function i(t){return"function"==typeof t.then}function a(t,e){return t===e||t!=t&&e!=e}var s="[object Arguments]",l="[object Boolean]",u="[object Date]",c="[object RegExp]",p="[object Map]",f="[object Number]",d="[object Object]",h="[object Set]",g="[object String]",y=Object.prototype.toString;function m(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areMapsEqual,m=t.areObjectsEqual,b=t.areRegExpsEqual,v=t.areSetsEqual,w=(0,t.createIsNestedEqual)(O);function O(t,O,S){if(t===O)return!0;if(!t||!O||"object"!=typeof t||"object"!=typeof O)return t!=t&&O!=O;if(o(t)&&o(O))return m(t,O,w,S);var x=Array.isArray(t),D=Array.isArray(O);if(x||D)return x===D&&e(t,O,w,S);var P=y.call(t);return P===y.call(O)&&(P===u?r(t,O,w,S):P===c?b(t,O,w,S):P===p?n(t,O,w,S):P===h?v(t,O,w,S):P===d||P===s?!i(t)&&!i(O)&&m(t,O,w,S):(P===l||P===f||P===g)&&a(t.valueOf(),O.valueOf()))}return O}function b(t,e,r,n){var o=t.length;if(e.length!==o)return!1;for(;o-- >0;)if(!r(t[o],e[o],o,o,t,e,n))return!1;return!0}var v=r(b);function w(t,e){return a(t.valueOf(),e.valueOf())}function O(t,e,r,n){var o=t.size===e.size;if(!o)return!1;if(!t.size)return!0;var i={},a=0;return t.forEach((function(s,l){if(o){var u=!1,c=0;e.forEach((function(o,p){u||i[c]||!(u=r(l,p,a,c,t,e,n)&&r(s,o,l,p,t,e,n))||(i[c]=!0),c++})),a++,o=u}})),o}var S=r(O),x=Object.prototype.hasOwnProperty;function D(t,e,r,n){var o,i=Object.keys(t),a=i.length;if(Object.keys(e).length!==a)return!1;for(;a-- >0;){if("_owner"===(o=i[a])){var s=!!t.$$typeof,l=!!e.$$typeof;if((s||l)&&s!==l)return!1}if(!x.call(e,o)||!r(t[o],e[o],o,o,t,e,n))return!1}return!0}var P=r(D);function R(t,e){return t.source===e.source&&t.flags===e.flags}function j(t,e,r,n){var o=t.size===e.size;if(!o)return!1;if(!t.size)return!0;var i={};return t.forEach((function(a,s){if(o){var l=!1,u=0;e.forEach((function(o,c){l||i[u]||!(l=r(a,o,s,c,t,e,n))||(i[u]=!0),u++})),o=l}})),o}var E=r(j),z=Object.freeze({areArraysEqual:b,areDatesEqual:w,areMapsEqual:O,areObjectsEqual:D,areRegExpsEqual:R,areSetsEqual:j,createIsNestedEqual:e}),C=Object.freeze({areArraysEqual:v,areDatesEqual:w,areMapsEqual:S,areObjectsEqual:P,areRegExpsEqual:R,areSetsEqual:E,createIsNestedEqual:e}),_=m(z);var M=m(n(z,{createIsNestedEqual:function(){return a}}));var T=m(C);var k=m(n(C,{createIsNestedEqual:function(){return a}}));t.circularDeepEqual=function(t,e){return T(t,e,new WeakMap)},t.circularShallowEqual=function(t,e){return k(t,e,new WeakMap)},t.createCustomCircularEqual=function(t){var e=m(n(C,t(C)));return function(t,r,n){return void 0===n&&(n=new WeakMap),e(t,r,n)}},t.createCustomEqual=function(t){return m(n(z,t(z)))},t.deepEqual=function(t,e){return _(t,e,void 0)},t.sameValueZeroEqual=a,t.shallowEqual=function(t,e){return M(t,e,void 0)},Object.defineProperty(t,"__esModule",{value:!0})}(e)},703:(t,e,r)=>{"use strict";var n=r(414);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},697:(t,e,r)=>{t.exports=r(703)()},414:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},668:(t,e,r)=>{"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return p.default}}),e.default=void 0;var o=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!==n(t)&&"function"!=typeof t)return{default:t};var r=g(e);if(r&&r.has(t))return r.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&Object.prototype.hasOwnProperty.call(t,a)){var s=i?Object.getOwnPropertyDescriptor(t,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=t[a]}return o.default=t,r&&r.set(t,o),o}(r(359)),i=h(r(697)),a=h(r(318)),s=h(r(946)),l=r(825),u=r(849),c=r(280),p=h(r(783)),f=h(r(904)),d=["axis","bounds","children","defaultPosition","defaultClassName","defaultClassNameDragging","defaultClassNameDragged","position","positionOffset","scale"];function h(t){return t&&t.__esModule?t:{default:t}}function g(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(g=function(t){return t?r:e})(t)}function y(){return y=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},y.apply(this,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){D(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function O(t,e){return O=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},O(t,e)}function S(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function x(t){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},x(t)}function D(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var P=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&O(t,e)}(m,t);var e,r,i,c,h,g=(c=m,h=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=x(c);if(h){var r=x(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return S(t)}(this,t)});function m(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,m),D(S(e=g.call(this,t)),"onDragStart",(function(t,r){if((0,f.default)("Draggable: onDragStart: %j",r),!1===e.props.onStart(t,(0,u.createDraggableData)(S(e),r)))return!1;e.setState({dragging:!0,dragged:!0})})),D(S(e),"onDrag",(function(t,r){if(!e.state.dragging)return!1;(0,f.default)("Draggable: onDrag: %j",r);var n,o,i=(0,u.createDraggableData)(S(e),r),a={x:i.x,y:i.y};if(e.props.bounds){var s=a.x,l=a.y;a.x+=e.state.slackX,a.y+=e.state.slackY;var c=(n=(0,u.getBoundPosition)(S(e),a.x,a.y),o=2,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}}(n,o)||function(t,e){if(t){if("string"==typeof t)return v(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=c[0],d=c[1];a.x=p,a.y=d,a.slackX=e.state.slackX+(s-a.x),a.slackY=e.state.slackY+(l-a.y),i.x=a.x,i.y=a.y,i.deltaX=a.x-e.state.x,i.deltaY=a.y-e.state.y}if(!1===e.props.onDrag(t,i))return!1;e.setState(a)})),D(S(e),"onDragStop",(function(t,r){if(!e.state.dragging)return!1;if(!1===e.props.onStop(t,(0,u.createDraggableData)(S(e),r)))return!1;(0,f.default)("Draggable: onDragStop: %j",r);var n={dragging:!1,slackX:0,slackY:0};if(Boolean(e.props.position)){var o=e.props.position,i=o.x,a=o.y;n.x=i,n.y=a}e.setState(n)})),e.state={dragging:!1,dragged:!1,x:t.position?t.position.x:t.defaultPosition.x,y:t.position?t.position.y:t.defaultPosition.y,prevPropsPosition:b({},t.position),slackX:0,slackY:0,isElementSVG:!1},!t.position||t.onDrag||t.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element."),e}return e=m,i=[{key:"getDerivedStateFromProps",value:function(t,e){var r=t.position,n=e.prevPropsPosition;return!r||n&&r.x===n.x&&r.y===n.y?null:((0,f.default)("Draggable: getDerivedStateFromProps %j",{position:r,prevPropsPosition:n}),{x:r.x,y:r.y,prevPropsPosition:b({},r)})}}],(r=[{key:"componentDidMount",value:function(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}},{key:"componentWillUnmount",value:function(){this.setState({dragging:!1})}},{key:"findDOMNode",value:function(){var t,e,r;return null!==(t=null===(e=this.props)||void 0===e||null===(r=e.nodeRef)||void 0===r?void 0:r.current)&&void 0!==t?t:a.default.findDOMNode(this)}},{key:"render",value:function(){var t,e=this.props,r=(e.axis,e.bounds,e.children),n=e.defaultPosition,i=e.defaultClassName,a=e.defaultClassNameDragging,c=e.defaultClassNameDragged,f=e.position,h=e.positionOffset,g=(e.scale,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,d)),m={},v=null,w=!Boolean(f)||this.state.dragging,O=f||n,S={x:(0,u.canDragX)(this)&&w?this.state.x:O.x,y:(0,u.canDragY)(this)&&w?this.state.y:O.y};this.state.isElementSVG?v=(0,l.createSVGTransform)(S,h):m=(0,l.createCSSTransform)(S,h);var x=(0,s.default)(r.props.className||"",i,(D(t={},a,this.state.dragging),D(t,c,this.state.dragged),t));return o.createElement(p.default,y({},g,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),o.cloneElement(o.Children.only(r),{className:x,style:b(b({},r.props.style),m),transform:v}))}}])&&w(e.prototype,r),i&&w(e,i),Object.defineProperty(e,"prototype",{writable:!1}),m}(o.Component);e.default=P,D(P,"displayName","Draggable"),D(P,"propTypes",b(b({},p.default.propTypes),{},{axis:i.default.oneOf(["both","x","y","none"]),bounds:i.default.oneOfType([i.default.shape({left:i.default.number,right:i.default.number,top:i.default.number,bottom:i.default.number}),i.default.string,i.default.oneOf([!1])]),defaultClassName:i.default.string,defaultClassNameDragging:i.default.string,defaultClassNameDragged:i.default.string,defaultPosition:i.default.shape({x:i.default.number,y:i.default.number}),positionOffset:i.default.shape({x:i.default.oneOfType([i.default.number,i.default.string]),y:i.default.oneOfType([i.default.number,i.default.string])}),position:i.default.shape({x:i.default.number,y:i.default.number}),className:c.dontSetMe,style:c.dontSetMe,transform:c.dontSetMe})),D(P,"defaultProps",b(b({},p.default.defaultProps),{},{axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1}))},783:(t,e,r)=>{"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!==n(t)&&"function"!=typeof t)return{default:t};var r=f(e);if(r&&r.has(t))return r.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&Object.prototype.hasOwnProperty.call(t,a)){var s=i?Object.getOwnPropertyDescriptor(t,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=t[a]}return o.default=t,r&&r.set(t,o),o}(r(359)),i=p(r(697)),a=p(r(318)),s=r(825),l=r(849),u=r(280),c=p(r(904));function p(t){return t&&t.__esModule?t:{default:t}}function f(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(f=function(t){return t?r:e})(t)}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function y(t,e){return y=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},y(t,e)}function m(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function b(t){return b=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},b(t)}function v(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var w={start:"touchstart",move:"touchmove",stop:"touchend"},O={start:"mousedown",move:"mousemove",stop:"mouseup"},S=O,x=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y(t,e)}(f,t);var e,r,i,u,p=(i=f,u=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=b(i);if(u){var r=b(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return m(t)}(this,t)});function f(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,f);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return v(m(t=p.call.apply(p,[this].concat(r))),"state",{dragging:!1,lastX:NaN,lastY:NaN,touchIdentifier:null}),v(m(t),"mounted",!1),v(m(t),"handleDragStart",(function(e){if(t.props.onMouseDown(e),!t.props.allowAnyClick&&"number"==typeof e.button&&0!==e.button)return!1;var r=t.findDOMNode();if(!r||!r.ownerDocument||!r.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");var n=r.ownerDocument;if(!(t.props.disabled||!(e.target instanceof n.defaultView.Node)||t.props.handle&&!(0,s.matchesSelectorAndParentsTo)(e.target,t.props.handle,r)||t.props.cancel&&(0,s.matchesSelectorAndParentsTo)(e.target,t.props.cancel,r))){"touchstart"===e.type&&e.preventDefault();var o=(0,s.getTouchIdentifier)(e);t.setState({touchIdentifier:o});var i=(0,l.getControlPosition)(e,o,m(t));if(null!=i){var a=i.x,u=i.y,p=(0,l.createCoreData)(m(t),a,u);(0,c.default)("DraggableCore: handleDragStart: %j",p),(0,c.default)("calling",t.props.onStart),!1!==t.props.onStart(e,p)&&!1!==t.mounted&&(t.props.enableUserSelectHack&&(0,s.addUserSelectStyles)(n),t.setState({dragging:!0,lastX:a,lastY:u}),(0,s.addEvent)(n,S.move,t.handleDrag),(0,s.addEvent)(n,S.stop,t.handleDragStop))}}})),v(m(t),"handleDrag",(function(e){var r=(0,l.getControlPosition)(e,t.state.touchIdentifier,m(t));if(null!=r){var n=r.x,o=r.y;if(Array.isArray(t.props.grid)){var i=n-t.state.lastX,a=o-t.state.lastY,s=d((0,l.snapToGrid)(t.props.grid,i,a),2);if(i=s[0],a=s[1],!i&&!a)return;n=t.state.lastX+i,o=t.state.lastY+a}var u=(0,l.createCoreData)(m(t),n,o);if((0,c.default)("DraggableCore: handleDrag: %j",u),!1!==t.props.onDrag(e,u)&&!1!==t.mounted)t.setState({lastX:n,lastY:o});else try{t.handleDragStop(new MouseEvent("mouseup"))}catch(e){var p=document.createEvent("MouseEvents");p.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),t.handleDragStop(p)}}})),v(m(t),"handleDragStop",(function(e){if(t.state.dragging){var r=(0,l.getControlPosition)(e,t.state.touchIdentifier,m(t));if(null!=r){var n=r.x,o=r.y;if(Array.isArray(t.props.grid)){var i=n-t.state.lastX||0,a=o-t.state.lastY||0,u=d((0,l.snapToGrid)(t.props.grid,i,a),2);i=u[0],a=u[1],n=t.state.lastX+i,o=t.state.lastY+a}var p=(0,l.createCoreData)(m(t),n,o);if(!1===t.props.onStop(e,p)||!1===t.mounted)return!1;var f=t.findDOMNode();f&&t.props.enableUserSelectHack&&(0,s.removeUserSelectStyles)(f.ownerDocument),(0,c.default)("DraggableCore: handleDragStop: %j",p),t.setState({dragging:!1,lastX:NaN,lastY:NaN}),f&&((0,c.default)("DraggableCore: Removing handlers"),(0,s.removeEvent)(f.ownerDocument,S.move,t.handleDrag),(0,s.removeEvent)(f.ownerDocument,S.stop,t.handleDragStop))}}})),v(m(t),"onMouseDown",(function(e){return S=O,t.handleDragStart(e)})),v(m(t),"onMouseUp",(function(e){return S=O,t.handleDragStop(e)})),v(m(t),"onTouchStart",(function(e){return S=w,t.handleDragStart(e)})),v(m(t),"onTouchEnd",(function(e){return S=w,t.handleDragStop(e)})),t}return e=f,(r=[{key:"componentDidMount",value:function(){this.mounted=!0;var t=this.findDOMNode();t&&(0,s.addEvent)(t,w.start,this.onTouchStart,{passive:!1})}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.findDOMNode();if(t){var e=t.ownerDocument;(0,s.removeEvent)(e,O.move,this.handleDrag),(0,s.removeEvent)(e,w.move,this.handleDrag),(0,s.removeEvent)(e,O.stop,this.handleDragStop),(0,s.removeEvent)(e,w.stop,this.handleDragStop),(0,s.removeEvent)(t,w.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,s.removeUserSelectStyles)(e)}}},{key:"findDOMNode",value:function(){var t,e,r;return null!==(t=this.props)&&void 0!==t&&t.nodeRef?null===(e=this.props)||void 0===e||null===(r=e.nodeRef)||void 0===r?void 0:r.current:a.default.findDOMNode(this)}},{key:"render",value:function(){return o.cloneElement(o.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}])&&g(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),f}(o.Component);e.default=x,v(x,"displayName","DraggableCore"),v(x,"propTypes",{allowAnyClick:i.default.bool,disabled:i.default.bool,enableUserSelectHack:i.default.bool,offsetParent:function(t,e){if(t[e]&&1!==t[e].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:i.default.arrayOf(i.default.number),handle:i.default.string,cancel:i.default.string,nodeRef:i.default.object,onStart:i.default.func,onDrag:i.default.func,onStop:i.default.func,onMouseDown:i.default.func,scale:i.default.number,className:u.dontSetMe,style:u.dontSetMe,transform:u.dontSetMe}),v(x,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},193:(t,e,r)=>{"use strict";var n=r(668),o=n.default,i=n.DraggableCore;t.exports=o,t.exports.default=o,t.exports.DraggableCore=i},825:(t,e,r)=>{"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.addClassName=d,e.addEvent=function(t,e,r,n){if(t){var o=l({capture:!0},n);t.addEventListener?t.addEventListener(e,r,o):t.attachEvent?t.attachEvent("on"+e,r):t["on"+e]=r}},e.addUserSelectStyles=function(t){if(t){var e=t.getElementById("react-draggable-style-el");e||((e=t.createElement("style")).type="text/css",e.id="react-draggable-style-el",e.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",e.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",t.getElementsByTagName("head")[0].appendChild(e)),t.body&&d(t.body,"react-draggable-transparent-selection")}},e.createCSSTransform=function(t,e){var r=f(t,e,"px");return u({},(0,i.browserPrefixToKey)("transform",i.default),r)},e.createSVGTransform=function(t,e){return f(t,e,"")},e.getTouch=function(t,e){return t.targetTouches&&(0,o.findInArray)(t.targetTouches,(function(t){return e===t.identifier}))||t.changedTouches&&(0,o.findInArray)(t.changedTouches,(function(t){return e===t.identifier}))},e.getTouchIdentifier=function(t){return t.targetTouches&&t.targetTouches[0]?t.targetTouches[0].identifier:t.changedTouches&&t.changedTouches[0]?t.changedTouches[0].identifier:void 0},e.getTranslation=f,e.innerHeight=function(t){var e=t.clientHeight,r=t.ownerDocument.defaultView.getComputedStyle(t);return(e-=(0,o.int)(r.paddingTop))-(0,o.int)(r.paddingBottom)},e.innerWidth=function(t){var e=t.clientWidth,r=t.ownerDocument.defaultView.getComputedStyle(t);return(e-=(0,o.int)(r.paddingLeft))-(0,o.int)(r.paddingRight)},e.matchesSelector=p,e.matchesSelectorAndParentsTo=function(t,e,r){var n=t;do{if(p(n,e))return!0;if(n===r)return!1;n=n.parentNode}while(n);return!1},e.offsetXYFromParent=function(t,e,r){var n=e===e.ownerDocument.body?{left:0,top:0}:e.getBoundingClientRect();return{x:(t.clientX+e.scrollLeft-n.left)/r,y:(t.clientY+e.scrollTop-n.top)/r}},e.outerHeight=function(t){var e=t.clientHeight,r=t.ownerDocument.defaultView.getComputedStyle(t);return(e+=(0,o.int)(r.borderTopWidth))+(0,o.int)(r.borderBottomWidth)},e.outerWidth=function(t){var e=t.clientWidth,r=t.ownerDocument.defaultView.getComputedStyle(t);return(e+=(0,o.int)(r.borderLeftWidth))+(0,o.int)(r.borderRightWidth)},e.removeClassName=h,e.removeEvent=function(t,e,r,n){if(t){var o=l({capture:!0},n);t.removeEventListener?t.removeEventListener(e,r,o):t.detachEvent?t.detachEvent("on"+e,r):t["on"+e]=null}},e.removeUserSelectStyles=function(t){if(t)try{if(t.body&&h(t.body,"react-draggable-transparent-selection"),t.selection)t.selection.empty();else{var e=(t.defaultView||window).getSelection();e&&"Caret"!==e.type&&e.removeAllRanges()}}catch(t){}};var o=r(280),i=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!==n(t)&&"function"!=typeof t)return{default:t};var r=a(e);if(r&&r.has(t))return r.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&Object.prototype.hasOwnProperty.call(t,s)){var l=i?Object.getOwnPropertyDescriptor(t,s):null;l&&(l.get||l.set)?Object.defineProperty(o,s,l):o[s]=t[s]}return o.default=t,r&&r.set(t,o),o}(r(650));function a(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(a=function(t){return t?r:e})(t)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var c="";function p(t,e){return c||(c=(0,o.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(e){return(0,o.isFunction)(t[e])}))),!!(0,o.isFunction)(t[c])&&t[c](e)}function f(t,e,r){var n=t.x,o=t.y,i="translate(".concat(n).concat(r,",").concat(o).concat(r,")");if(e){var a="".concat("string"==typeof e.x?e.x:e.x+r),s="".concat("string"==typeof e.y?e.y:e.y+r);i="translate(".concat(a,", ").concat(s,")")+i}return i}function d(t,e){t.classList?t.classList.add(e):t.className.match(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)")))||(t.className+=" ".concat(e))}function h(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)"),"g"),"")}},650:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.browserPrefixToKey=o,e.browserPrefixToStyle=function(t,e){return e?"-".concat(e.toLowerCase(),"-").concat(t):t},e.default=void 0,e.getPrefix=n;var r=["Moz","Webkit","O","ms"];function n(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";var i=null===(t=window.document)||void 0===t||null===(e=t.documentElement)||void 0===e?void 0:e.style;if(!i)return"";if(n in i)return"";for(var a=0;a<r.length;a++)if(o(n,r[a])in i)return r[a];return""}function o(t,e){return e?"".concat(e).concat(function(t){for(var e="",r=!0,n=0;n<t.length;n++)r?(e+=t[n].toUpperCase(),r=!1):"-"===t[n]?r=!0:e+=t[n];return e}(t)):t}var i=n();e.default=i},904:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){}},849:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.canDragX=function(t){return"both"===t.props.axis||"x"===t.props.axis},e.canDragY=function(t){return"both"===t.props.axis||"y"===t.props.axis},e.createCoreData=function(t,e,r){var o=t.state,a=!(0,n.isNum)(o.lastX),s=i(t);return a?{node:s,deltaX:0,deltaY:0,lastX:e,lastY:r,x:e,y:r}:{node:s,deltaX:e-o.lastX,deltaY:r-o.lastY,lastX:o.lastX,lastY:o.lastY,x:e,y:r}},e.createDraggableData=function(t,e){var r=t.props.scale;return{node:e.node,x:t.state.x+e.deltaX/r,y:t.state.y+e.deltaY/r,deltaX:e.deltaX/r,deltaY:e.deltaY/r,lastX:t.state.x,lastY:t.state.y}},e.getBoundPosition=function(t,e,r){if(!t.props.bounds)return[e,r];var a=t.props.bounds;a="string"==typeof a?a:function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom}}(a);var s=i(t);if("string"==typeof a){var l,u=s.ownerDocument,c=u.defaultView;if(!((l="parent"===a?s.parentNode:u.querySelector(a))instanceof c.HTMLElement))throw new Error('Bounds selector "'+a+'" could not find an element.');var p=l,f=c.getComputedStyle(s),d=c.getComputedStyle(p);a={left:-s.offsetLeft+(0,n.int)(d.paddingLeft)+(0,n.int)(f.marginLeft),top:-s.offsetTop+(0,n.int)(d.paddingTop)+(0,n.int)(f.marginTop),right:(0,o.innerWidth)(p)-(0,o.outerWidth)(s)-s.offsetLeft+(0,n.int)(d.paddingRight)-(0,n.int)(f.marginRight),bottom:(0,o.innerHeight)(p)-(0,o.outerHeight)(s)-s.offsetTop+(0,n.int)(d.paddingBottom)-(0,n.int)(f.marginBottom)}}return(0,n.isNum)(a.right)&&(e=Math.min(e,a.right)),(0,n.isNum)(a.bottom)&&(r=Math.min(r,a.bottom)),(0,n.isNum)(a.left)&&(e=Math.max(e,a.left)),(0,n.isNum)(a.top)&&(r=Math.max(r,a.top)),[e,r]},e.getControlPosition=function(t,e,r){var n="number"==typeof e?(0,o.getTouch)(t,e):null;if("number"==typeof e&&!n)return null;var a=i(r),s=r.props.offsetParent||a.offsetParent||a.ownerDocument.body;return(0,o.offsetXYFromParent)(n||t,s,r.props.scale)},e.snapToGrid=function(t,e,r){return[Math.round(e/t[0])*t[0],Math.round(r/t[1])*t[1]]};var n=r(280),o=r(825);function i(t){var e=t.findDOMNode();if(!e)throw new Error("<DraggableCore>: Unmounted during event!");return e}},280:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.dontSetMe=function(t,e,r){if(t[e])return new Error("Invalid prop ".concat(e," passed to ").concat(r," - do not set this, set it on the child."))},e.findInArray=function(t,e){for(var r=0,n=t.length;r<n;r++)if(e.apply(e,[t[r],r,t]))return t[r]},e.int=function(t){return parseInt(t,10)},e.isFunction=function(t){return"function"==typeof t||"[object Function]"===Object.prototype.toString.call(t)},e.isNum=function(t){return"number"==typeof t&&!isNaN(t)}},946:(t,e,r)=>{"use strict";function n(t){var e,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(r=n(t[e]))&&(o&&(o+=" "),o+=r);else for(e in t)t[e]&&(o&&(o+=" "),o+=e);return o}function o(){for(var t,e,r=0,o="";r<arguments.length;)(t=arguments[r++])&&(e=n(t))&&(o&&(o+=" "),o+=e);return o}r.r(e),r.d(e,{clsx:()=>o,default:()=>i});const i=o},827:(t,e,r)=>{"use strict";e.__esModule=!0,e.default=void 0;var n=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=l(e);if(r&&r.has(t))return r.get(t);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=t[i]}return n.default=t,r&&r.set(t,n),n}(r(359)),o=r(193),i=r(69),a=r(448),s=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function l(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(l=function(t){return t?r:e})(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){return d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},d(t,e)}var h=function(t){var e,r;function a(){for(var e,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(e=t.call.apply(t,[this].concat(n))||this).handleRefs={},e.lastHandleRect=null,e.slack=null,e}r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,d(e,r);var l=a.prototype;return l.componentWillUnmount=function(){this.resetData()},l.resetData=function(){this.lastHandleRect=this.slack=null},l.runConstraints=function(t,e){var r=this.props,n=r.minConstraints,o=r.maxConstraints,i=r.lockAspectRatio;if(!n&&!o&&!i)return[t,e];if(i){var a=this.props.width/this.props.height,s=t-this.props.width,l=e-this.props.height;Math.abs(s)>Math.abs(l*a)?e=t/a:t=e*a}var u=t,c=e,p=this.slack||[0,0],f=p[0],d=p[1];return t+=f,e+=d,n&&(t=Math.max(n[0],t),e=Math.max(n[1],e)),o&&(t=Math.min(o[0],t),e=Math.min(o[1],e)),this.slack=[f+(u-t),d+(c-e)],[t,e]},l.resizeHandler=function(t,e){var r=this;return function(n,o){var i=o.node,a=o.deltaX,s=o.deltaY;"onResizeStart"===t&&r.resetData();var l=("both"===r.props.axis||"x"===r.props.axis)&&"n"!==e&&"s"!==e,u=("both"===r.props.axis||"y"===r.props.axis)&&"e"!==e&&"w"!==e;if(l||u){var c=e[0],p=e[e.length-1],f=i.getBoundingClientRect();null!=r.lastHandleRect&&("w"===p&&(a+=f.left-r.lastHandleRect.left),"n"===c&&(s+=f.top-r.lastHandleRect.top)),r.lastHandleRect=f,"w"===p&&(a=-a),"n"===c&&(s=-s);var d=r.props.width+(l?a/r.props.transformScale:0),h=r.props.height+(u?s/r.props.transformScale:0),g=r.runConstraints(d,h);d=g[0],h=g[1];var y=d!==r.props.width||h!==r.props.height,m="function"==typeof r.props[t]?r.props[t]:null;m&&!("onResize"===t&&!y)&&(null==n.persist||n.persist(),m(n,{node:i,size:{width:d,height:h},handle:e})),"onResizeStop"===t&&r.resetData()}}},l.renderResizeHandle=function(t,e){var r=this.props.handle;if(!r)return n.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+t,ref:e});if("function"==typeof r)return r(t,e);var o=p({ref:e},"string"==typeof r.type?{}:{handleAxis:t});return n.cloneElement(r,o)},l.render=function(){var t=this,e=this.props,r=e.children,a=e.className,l=e.draggableOpts,c=(e.width,e.height,e.handle,e.handleSize,e.lockAspectRatio,e.axis,e.minConstraints,e.maxConstraints,e.onResize,e.onResizeStop,e.onResizeStart,e.resizeHandles),f=(e.transformScale,function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(e,s));return(0,i.cloneElement)(r,p(p({},f),{},{className:(a?a+" ":"")+"react-resizable",children:[].concat(r.props.children,c.map((function(e){var r,i=null!=(r=t.handleRefs[e])?r:t.handleRefs[e]=n.createRef();return n.createElement(o.DraggableCore,u({},l,{nodeRef:i,key:"resizableHandle-"+e,onStop:t.resizeHandler("onResizeStop",e),onStart:t.resizeHandler("onResizeStart",e),onDrag:t.resizeHandler("onResize",e)}),t.renderResizeHandle(e,i))})))}))},a}(n.Component);e.default=h,h.propTypes=a.resizableProps,h.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1}},735:(t,e,r)=>{"use strict";e.default=void 0;var n=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=u(e);if(r&&r.has(t))return r.get(t);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=t[i]}return n.default=t,r&&r.set(t,n),n}(r(359)),o=l(r(697)),i=l(r(827)),a=r(448),s=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function l(t){return t&&t.__esModule?t:{default:t}}function u(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(u=function(t){return t?r:e})(t)}function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}var g=function(t){var e,r;function o(){for(var e,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(e=t.call.apply(t,[this].concat(n))||this).state={width:e.props.width,height:e.props.height,propsWidth:e.props.width,propsHeight:e.props.height},e.onResize=function(t,r){var n=r.size;e.props.onResize?(null==t.persist||t.persist(),e.setState(n,(function(){return e.props.onResize&&e.props.onResize(t,r)}))):e.setState(n)},e}return r=t,(e=o).prototype=Object.create(r.prototype),e.prototype.constructor=e,h(e,r),o.getDerivedStateFromProps=function(t,e){return e.propsWidth!==t.width||e.propsHeight!==t.height?{width:t.width,height:t.height,propsWidth:t.width,propsHeight:t.height}:null},o.prototype.render=function(){var t=this.props,e=t.handle,r=t.handleSize,o=(t.onResize,t.onResizeStart),a=t.onResizeStop,l=t.draggableOpts,u=t.minConstraints,p=t.maxConstraints,d=t.lockAspectRatio,h=t.axis,g=(t.width,t.height,t.resizeHandles),y=t.style,m=t.transformScale,b=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,s);return n.createElement(i.default,{axis:h,draggableOpts:l,handle:e,handleSize:r,height:this.state.height,lockAspectRatio:d,maxConstraints:p,minConstraints:u,onResizeStart:o,onResize:this.onResize,onResizeStop:a,resizeHandles:g,transformScale:m,width:this.state.width},n.createElement("div",c({},b,{style:f(f({},y),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},o}(n.Component);e.default=g,g.propTypes=f(f({},a.resizableProps),{},{children:o.default.element})},448:(t,e,r)=>{"use strict";e.__esModule=!0,e.resizableProps=void 0;var n,o=(n=r(697))&&n.__esModule?n:{default:n};r(193);var i={axis:o.default.oneOf(["both","x","y","none"]),className:o.default.string,children:o.default.element.isRequired,draggableOpts:o.default.shape({allowAnyClick:o.default.bool,cancel:o.default.string,children:o.default.node,disabled:o.default.bool,enableUserSelectHack:o.default.bool,offsetParent:o.default.node,grid:o.default.arrayOf(o.default.number),handle:o.default.string,nodeRef:o.default.object,onStart:o.default.func,onDrag:o.default.func,onStop:o.default.func,onMouseDown:o.default.func,scale:o.default.number}),height:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=e[0];return"both"===i.axis||"y"===i.axis?(n=o.default.number).isRequired.apply(n,e):o.default.number.apply(o.default,e)},handle:o.default.oneOfType([o.default.node,o.default.func]),handleSize:o.default.arrayOf(o.default.number),lockAspectRatio:o.default.bool,maxConstraints:o.default.arrayOf(o.default.number),minConstraints:o.default.arrayOf(o.default.number),onResizeStop:o.default.func,onResizeStart:o.default.func,onResize:o.default.func,resizeHandles:o.default.arrayOf(o.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:o.default.number,width:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=e[0];return"both"===i.axis||"x"===i.axis?(n=o.default.number).isRequired.apply(n,e):o.default.number.apply(o.default,e)}};e.resizableProps=i},69:(t,e,r)=>{"use strict";e.__esModule=!0,e.cloneElement=function(t,e){return e.style&&t.props.style&&(e.style=a(a({},t.props.style),e.style)),e.className&&t.props.className&&(e.className=t.props.className+" "+e.className),o.default.cloneElement(t,e)};var n,o=(n=r(359))&&n.__esModule?n:{default:n};function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}},706:(t,e,r)=>{"use strict";t.exports=function(){throw new Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")},t.exports.Resizable=r(827).default,t.exports.ResizableBox=r(735).default},359:e=>{"use strict";e.exports=t},318:t=>{"use strict";t.exports=e},512:(t,e,r)=>{"use strict";function n(t){var e,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(r=n(t[e]))&&(o&&(o+=" "),o+=r);else for(e in t)t[e]&&(o&&(o+=" "),o+=e);return o}r.d(e,{Z:()=>o});const o=function(){for(var t,e,r=0,o="";r<arguments.length;)(t=arguments[r++])&&(e=n(t))&&(o&&(o+=" "),o+=e);return o}}},n={};function o(t){var e=n[t];if(void 0!==e)return e.exports;var i=n[t]={exports:{}};return r[t].call(i.exports,i,i.exports,o),i.exports}return o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o(325)})()));
//# sourceMappingURL=react-grid-layout.min.js.map