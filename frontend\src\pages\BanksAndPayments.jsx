import React, { useState } from 'react'
import { CreditCard, Wallet } from 'lucide-react'
import BankManager from '../components/BankManager'
import PaymentMethodManager from '../components/PaymentMethodManager'
import Sidebar from '../components/Sidebar'

function BanksAndPayments() {
  const [activeTab, setActiveTab] = useState('banks')
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">Gestão Financeira</h1>
              <p className="text-gray-600 mt-2">
                <PERSON><PERSON><PERSON><PERSON> seus bancos, contas e formas de pagamento
              </p>
            </div>

            {/* Tabs */}
            <div className="mb-8">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('banks')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'banks'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Wallet className="h-4 w-4" />
                      <span>Bancos e Contas</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('payments')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'payments'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>Formas de Pagamento</span>
                    </div>
                  </button>
                </nav>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              {activeTab === 'banks' && <BankManager />}
              {activeTab === 'payments' && <PaymentMethodManager />}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BanksAndPayments
