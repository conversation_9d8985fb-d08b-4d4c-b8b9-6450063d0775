import React, { useState, useEffect } from 'react'
import { Plus, Users, DollarSign, TrendingUp, TrendingDown, Search, Filter, Eye } from 'lucide-react'
import { contactService, loanService, loanUtils } from '../services/loanService'
import { bankService } from '../services/bankService'
import toast from 'react-hot-toast'
import Sidebar from '../components/Sidebar'
// import ContactCard from '../components/ContactCard'
// import LoanModal from '../components/LoanModal'
// import ContactModal from '../components/ContactModal'
// import ContactDetailModal from '../components/ContactDetailModal'

function Loans() {
  const [contacts, setContacts] = useState([])
  const [loans, setLoans] = useState([])
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')

  // Modais
  const [showContactModal, setShowContactModal] = useState(false)
  const [showLoanModal, setShowLoanModal] = useState(false)
  const [showContactDetail, setShowContactDetail] = useState(false)
  const [selectedContact, setSelectedContact] = useState(null)
  const [editingContact, setEditingContact] = useState(null)

  // Estatísticas
  const [stats, setStats] = useState({
    totalLoansGiven: 0,
    totalLoansReceived: 0,
    activeLoans: 0,
    completedLoans: 0
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [contactsData, loansData, banksData] = await Promise.all([
        contactService.getContacts(),
        loanService.getLoans(),
        bankService.getBanks()
      ])

      setContacts(contactsData)
      setLoans(loansData)
      setBanks(banksData)
      calculateStats(loansData)
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (loansData) => {
    const stats = {
      totalLoansGiven: 0,
      totalLoansReceived: 0,
      activeLoans: 0,
      completedLoans: 0
    }

    loansData.forEach(loan => {
      if (loan.type === 'LOAN_GIVEN') {
        stats.totalLoansGiven += loan.totalAmount
      } else {
        stats.totalLoansReceived += loan.totalAmount
      }

      if (loan.status === 'ACTIVE') {
        stats.activeLoans++
      } else if (loan.status === 'COMPLETED') {
        stats.completedLoans++
      }
    })

    setStats(stats)
  }

  const handleCreateContact = () => {
    setEditingContact(null)
    setShowContactModal(true)
  }

  const handleEditContact = (contact) => {
    setEditingContact(contact)
    setShowContactModal(true)
  }

  const handleViewContact = (contact) => {
    setSelectedContact(contact)
    setShowContactDetail(true)
  }

  const handleCreateLoan = (contact = null) => {
    setSelectedContact(contact)
    setShowLoanModal(true)
  }

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || contact.status === statusFilter
    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className="flex-1 overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Empréstimos e Dívidas</h1>
                <p className="text-gray-600 mt-1">Gerencie seus empréstimos e contratos</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleCreateContact}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Users className="h-4 w-4" />
                  Novo Contato
                </button>
                <button
                  onClick={() => handleCreateLoan()}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Novo Empréstimo
                </button>
              </div>
            </div>

            {/* Estatísticas */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Emprestei</p>
                    <p className="text-2xl font-bold text-red-600">
                      {loanUtils.formatCurrency(stats.totalLoansGiven)}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Peguei Emprestado</p>
                    <p className="text-2xl font-bold text-green-600">
                      {loanUtils.formatCurrency(stats.totalLoansReceived)}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Empréstimos Ativos</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.activeLoans}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Quitados</p>
                    <p className="text-2xl font-bold text-gray-600">{stats.completedLoans}</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-gray-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Filtros */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Buscar contatos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos os Status</option>
                <option value="GOOD">Bom Pagador</option>
                <option value="NEUTRAL">Neutro</option>
                <option value="BAD">Mau Pagador</option>
              </select>
            </div>
          </div>

          {/* Lista de Contatos */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredContacts.map((contact) => (
              <div key={contact.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{contact.name}</h3>
                    <p className="text-sm text-gray-600">{loanUtils.getContactStatusLabel(contact.status)}</p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  {contact.email && (
                    <p className="text-sm text-gray-600">📧 {contact.email}</p>
                  )}
                  {contact.phone && (
                    <p className="text-sm text-gray-600">📞 {contact.phone}</p>
                  )}
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => handleViewContact(contact)}
                    className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100"
                  >
                    Ver
                  </button>
                  <button
                    onClick={() => handleCreateLoan(contact)}
                    className="px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100"
                  >
                    Empréstimo
                  </button>
                </div>
              </div>
            ))}

            {filteredContacts.length === 0 && (
              <div className="col-span-full text-center py-12">
                <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum contato encontrado
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter ? 'Tente ajustar os filtros' : 'Comece criando seu primeiro contato'}
                </p>
                <button
                  onClick={handleCreateContact}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Criar Primeiro Contato
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modais - Em desenvolvimento */}
      {showContactModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">
              {editingContact ? 'Editar Contato' : 'Novo Contato'}
            </h3>
            <p className="text-gray-600 mb-4">Modal em desenvolvimento...</p>
            <button
              onClick={() => setShowContactModal(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Fechar
            </button>
          </div>
        </div>
      )}

      {showLoanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Novo Empréstimo</h3>
            <p className="text-gray-600 mb-4">Modal em desenvolvimento...</p>
            <button
              onClick={() => setShowLoanModal(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Fechar
            </button>
          </div>
        </div>
      )}

      {showContactDetail && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Detalhes do Contato</h3>
            <p className="text-gray-600 mb-4">Modal em desenvolvimento...</p>
            <button
              onClick={() => setShowContactDetail(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Fechar
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default Loans
