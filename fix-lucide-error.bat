@echo off
echo ========================================
echo    🔧 Correção do Erro Lucide-React
echo ========================================
echo.
echo Corrigindo erro de ícone HandHeart...
echo.

echo ✅ Problemas identificados e corrigidos:
echo    • HandHeart não existe no lucide-react
echo    • Substituído por Heart
echo    • vite.svg criado na pasta public
echo    • App.jsx restaurado para versão completa
echo.

echo 🔄 Reiniciando servidor...
cd frontend

echo Parando processos existentes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo.
echo 🚀 Iniciando servidor corrigido...
echo.
echo 📍 URLs disponíveis:
echo    • http://localhost:5173/ (Dashboard)
echo    • http://localhost:5173/loans (Empréstimos)
echo    • http://localhost:5173/transactions (Transações)
echo    • http://localhost:5173/banks (Bancos)
echo.
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.

timeout /t 2 /nobreak >nul
start http://localhost:5173/
call npm run dev

cd ..
