// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  transactions Transaction[]
  categories   Category[]
  dashboardLayouts DashboardLayout[]
  dashboardCards DashboardCard[]
  dashboardProfiles DashboardProfile[]

  @@map("users")
}

model Category {
  id     String @id @default(cuid())
  name   String
  color  String @default("#3B82F6")
  icon   String @default("💰")
  userId String

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("categories")
}

model Transaction {
  id          String   @id @default(cuid())
  description String
  amount      Float
  type        String   // INCOME, EXPENSE, INVESTMENT, LOAN
  date        DateTime @default(now())
  categoryId  String?
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  category Category? @relation(fields: [categoryId], references: [id])

  @@map("transactions")
}

model DashboardLayout {
  id       String @id @default(cuid())
  userId   String
  page     String // 'dashboard' ou 'analytics'
  layout   String // JSON com configurações do layout
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, page])
  @@map("dashboard_layouts")
}

model DashboardCard {
  id          String @id @default(cuid())
  userId      String
  profileId   String?
  title       String
  type        String // 'numeric', 'pie', 'table', 'chart'
  position    String // JSON com x, y, w, h
  config      String // JSON com configurações específicas do card
  categories  String // JSON com IDs das categorias conectadas
  visible     Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  profile DashboardProfile? @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("dashboard_cards")
}

model DashboardProfile {
  id          String @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  cards DashboardCard[]

  @@map("dashboard_profiles")
}
