import React, { useState, useEffect } from 'react'
import {
  ChevronDown,
  Plus,
  <PERSON><PERSON>s,
  Eye,
  EyeOff,
  <PERSON><PERSON>,
  Maximize2,
  Minimize2,
  Move,
  Filter
} from 'lucide-react'
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'
import api from '../services/api'
import toast from 'react-hot-toast'

function DashboardAnalytics({ selectedYear }) {
  const [analyticsData, setAnalyticsData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [showMonthDropdown, setShowMonthDropdown] = useState(false)
  const [layout, setLayout] = useState({ charts: [] })
  const [categories, setCategories] = useState([])
  const [selectedCategories, setSelectedCategories] = useState([])
  const [showFilters, setShowFilters] = useState(false)

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abri<PERSON>', 'Mai<PERSON>', 'Jun<PERSON>',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  useEffect(() => {
    fetchAnalyticsData()
    fetchLayout()
    fetchCategories()
  }, [selectedYear, selectedMonth])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/analytics?year=${selectedYear || new Date().getFullYear()}&month=${selectedMonth}`)
      setAnalyticsData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados analíticos:', error)
      toast.error('Erro ao carregar dados analíticos')
    } finally {
      setLoading(false)
    }
  }

  const fetchLayout = async () => {
    try {
      const response = await api.get('/layout/analytics')
      setLayout(response.data.layout)
    } catch (error) {
      console.error('Erro ao buscar layout:', error)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
      setSelectedCategories(response.data.map(cat => cat.id))
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const saveLayout = async (newLayout) => {
    try {
      await api.post('/layout/analytics', { layout: newLayout })
      setLayout(newLayout)
    } catch (error) {
      console.error('Erro ao salvar layout:', error)
      toast.error('Erro ao salvar layout')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const toggleChartVisibility = (chartId) => {
    const newLayout = {
      ...layout,
      charts: layout.charts.map(chart =>
        chart.id === chartId
          ? { ...chart, visible: !chart.visible }
          : chart
      )
    }
    saveLayout(newLayout)
  }

  const updateChartSize = (chartId, size) => {
    const newLayout = {
      ...layout,
      charts: layout.charts.map(chart =>
        chart.id === chartId
          ? { ...chart, size }
          : chart
      )
    }
    saveLayout(newLayout)
  }

  const updateChartColor = (chartId, color) => {
    const newLayout = {
      ...layout,
      charts: layout.charts.map(chart =>
        chart.id === chartId
          ? { ...chart, color }
          : chart
      )
    }
    saveLayout(newLayout)
  }

  const moveChart = (chartId, direction) => {
    const charts = [...layout.charts]
    const index = charts.findIndex(chart => chart.id === chartId)

    if (direction === 'up' && index > 0) {
      [charts[index], charts[index - 1]] = [charts[index - 1], charts[index]]
    } else if (direction === 'down' && index < charts.length - 1) {
      [charts[index], charts[index + 1]] = [charts[index + 1], charts[index]]
    }

    const newLayout = { ...layout, charts }
    saveLayout(newLayout)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Erro ao carregar dados analíticos</p>
      </div>
    )
  }

  const { summary, charts } = analyticsData

  return (
    <div className="space-y-6 fade-in">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>

          {/* Seletor de Mês */}
          <div className="relative">
            <button
              onClick={() => setShowMonthDropdown(!showMonthDropdown)}
              className="flex items-center px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <span className="font-medium">{months[selectedMonth - 1]}</span>
              <ChevronDown className="ml-2 h-4 w-4" />
            </button>

            {showMonthDropdown && (
              <div className="absolute left-0 mt-2 w-40 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                {months.map((month, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedMonth(index + 1)
                      setShowMonthDropdown(false)
                    }}
                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                      index + 1 === selectedMonth ? 'bg-gray-50 font-medium' : ''
                    }`}
                  >
                    {month}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Valor Total e Controles */}
        <div className="flex items-center space-x-4">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-lg">
            <span className="text-sm">Valor Total</span>
            <div className="text-lg font-bold">{formatCurrency(summary.totalExpenses)}</div>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </button>
        </div>
      </div>

      {/* Cards de Resumo */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {categories.slice(0, 6).map((category) => {
          const categoryData = analyticsData.categories.find(cat => cat.id === category.id)
          const value = categoryData ? categoryData.expense : 0

          return (
            <div key={category.id} className="bg-gray-800 text-white rounded-lg p-4 text-center">
              <div className="text-2xl mb-2">{category.icon}</div>
              <div className="text-sm font-medium mb-1">{category.name}</div>
              <div className="text-lg font-bold">{formatCurrency(value)}</div>
            </div>
          )
        })}
      </div>

      {/* Filtros */}
      {showFilters && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filtros</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {categories.map((category) => (
              <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedCategories.includes(category.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedCategories([...selectedCategories, category.id])
                    } else {
                      setSelectedCategories(selectedCategories.filter(id => id !== category.id))
                    }
                  }}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">
                  {category.icon} {category.name}
                </span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {layout.charts?.filter(chart => chart.visible).map((chartConfig) => {
          const sizeClasses = {
            small: 'lg:col-span-1',
            medium: 'lg:col-span-1',
            large: 'lg:col-span-2 xl:col-span-3'
          }

          return (
            <div
              key={chartConfig.id}
              className={`bg-white rounded-xl shadow-sm border border-gray-100 p-6 ${sizeClasses[chartConfig.size] || 'lg:col-span-1'}`}
            >
              {/* Header do Gráfico */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{chartConfig.title}</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => moveChart(chartConfig.id, 'up')}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Mover para cima"
                  >
                    <Move className="h-4 w-4" />
                  </button>

                  <div className="relative group">
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Settings className="h-4 w-4" />
                    </button>

                    {/* Menu de configurações */}
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                      <div className="p-2">
                        <div className="mb-2">
                          <label className="block text-xs font-medium text-gray-700 mb-1">Tamanho</label>
                          <select
                            value={chartConfig.size}
                            onChange={(e) => updateChartSize(chartConfig.id, e.target.value)}
                            className="w-full text-xs border border-gray-300 rounded px-2 py-1"
                          >
                            <option value="small">Pequeno</option>
                            <option value="medium">Médio</option>
                            <option value="large">Grande</option>
                          </select>
                        </div>

                        <div className="mb-2">
                          <label className="block text-xs font-medium text-gray-700 mb-1">Cor</label>
                          <div className="flex space-x-1">
                            {['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'].map(color => (
                              <button
                                key={color}
                                onClick={() => updateChartColor(chartConfig.id, color)}
                                className="w-6 h-6 rounded border-2"
                                style={{
                                  backgroundColor: color,
                                  borderColor: chartConfig.color === color ? '#000' : '#ccc'
                                }}
                              />
                            ))}
                          </div>
                        </div>

                        <button
                          onClick={() => toggleChartVisibility(chartConfig.id)}
                          className="w-full text-xs text-red-600 hover:text-red-700 py-1"
                        >
                          Ocultar Gráfico
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Conteúdo do Gráfico */}
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  {chartConfig.type === 'pie' && chartConfig.id === 'expenses-by-category' && (
                    <PieChart>
                      <Pie
                        data={charts.expensesByCategory.filter(item =>
                          selectedCategories.includes(categories.find(cat => cat.name === item.name)?.id)
                        )}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={100}
                        paddingAngle={2}
                        dataKey="value"
                      >
                        {charts.expensesByCategory.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [formatCurrency(value), 'Gasto']} />
                    </PieChart>
                  )}

                  {chartConfig.type === 'bar' && chartConfig.id === 'weekly-expenses' && (
                    <BarChart data={charts.weeklyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip formatter={(value) => [formatCurrency(value), 'Gasto']} />
                      <Bar dataKey="expense" fill={chartConfig.color} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  )}

                  {chartConfig.type === 'line' && chartConfig.id === 'daily-flow' && (
                    <LineChart data={charts.dailyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip formatter={(value) => [formatCurrency(value)]} />
                      <Line
                        type="monotone"
                        dataKey="expense"
                        stroke="#EF4444"
                        strokeWidth={2}
                        name="Gastos"
                      />
                      <Line
                        type="monotone"
                        dataKey="income"
                        stroke="#10B981"
                        strokeWidth={2}
                        name="Receitas"
                      />
                    </LineChart>
                  )}
                </ResponsiveContainer>
              </div>
            </div>
          )
        })}
      </div>

      {/* Gráficos Ocultos */}
      {layout.charts?.filter(chart => !chart.visible).length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Gráficos Ocultos</h3>
          <div className="flex flex-wrap gap-2">
            {layout.charts.filter(chart => !chart.visible).map((chart) => (
              <button
                key={chart.id}
                onClick={() => toggleChartVisibility(chart.id)}
                className="flex items-center px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                {chart.title}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default DashboardAnalytics
