import React, { useState, useEffect, useRef } from 'react'
import {
  ChevronDown,
  Plus,
  Grid3X3,
  LayoutDashboard,
  Settings,
  User
} from 'lucide-react'
import DashboardCard from '../components/DashboardCard'
import AddCardModal from '../components/AddCardModal'
import TransactionModal from '../components/TransactionModal'
import ProfileManager from '../components/ProfileManager'
import api from '../services/api'
import toast from 'react-hot-toast'

function DashboardAnalytics({ selectedYear }) {
  const [cards, setCards] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [showMonthDropdown, setShowMonthDropdown] = useState(false)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showTransactionModal, setShowTransactionModal] = useState(false)
  const [showProfileManager, setShowProfileManager] = useState(false)
  const [transactionModalData, setTransactionModalData] = useState({})
  const [draggedCard, setDraggedCard] = useState(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [currentProfile, setCurrentProfile] = useState(null)
  const [profiles, setProfiles] = useState([])
  const containerRef = useRef(null)

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  useEffect(() => {
    fetchProfiles()
  }, [])

  useEffect(() => {
    if (currentProfile) {
      fetchCards()
    }
  }, [selectedYear, selectedMonth, currentProfile])

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (isDragging && draggedCard) {
        e.preventDefault()
        const rect = containerRef.current?.getBoundingClientRect()
        if (rect) {
          const x = Math.max(0, Math.min(rect.width - 300, e.clientX - rect.left - dragOffset.x))
          const y = Math.max(0, Math.min(rect.height - 200, e.clientY - rect.top - dragOffset.y))

          setCards(prev => prev.map(card =>
            card.id === draggedCard.id
              ? { ...card, position: { ...card.position, x, y } }
              : card
          ))
        }
      }
    }

    const handleMouseUp = (e) => {
      if (isDragging && draggedCard) {
        e.preventDefault()
        setIsDragging(false)

        // Salvar posições após um pequeno delay para evitar múltiplas chamadas
        setTimeout(() => {
          saveCardPositions()
          setDraggedCard(null)
        }, 100)
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove, { passive: false })
      document.addEventListener('mouseup', handleMouseUp, { passive: false })
      document.body.style.cursor = 'grabbing'
      document.body.style.userSelect = 'none'
    } else {
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, draggedCard, dragOffset])

  const fetchProfiles = async () => {
    try {
      const response = await api.get('/dashboard-profiles')
      setProfiles(response.data)

      // Selecionar perfil padrão se não houver um selecionado
      if (!currentProfile && response.data.length > 0) {
        const defaultProfile = response.data.find(p => p.isDefault) || response.data[0]
        setCurrentProfile(defaultProfile)
      }
    } catch (error) {
      console.error('Erro ao buscar perfis:', error)
      toast.error('Erro ao carregar perfis')
    }
  }

  const fetchCards = async () => {
    if (!currentProfile) return

    try {
      setLoading(true)
      const response = await api.get(`/dashboard-cards?profileId=${currentProfile.id}`)
      setCards(response.data)
    } catch (error) {
      console.error('Erro ao buscar cards:', error)
      toast.error('Erro ao carregar cards')
    } finally {
      setLoading(false)
    }
  }

  const saveCardPositions = async () => {
    try {
      const cardPositions = cards.map(card => ({
        id: card.id,
        position: card.position
      }))

      await api.put('/dashboard-cards/positions/bulk', { cards: cardPositions })
    } catch (error) {
      console.error('Erro ao salvar posições:', error)
      toast.error('Erro ao salvar posições dos cards')
    }
  }

  const handleCardAdded = (newCard) => {
    setCards(prev => [...prev, newCard])
    toast.success('Card adicionado com sucesso!')
  }

  const handleProfileChange = (profile) => {
    setCurrentProfile(profile)
    setShowProfileManager(false)
  }

  const handleCardDelete = async (cardId) => {
    try {
      await api.delete(`/dashboard-cards/${cardId}`)
      setCards(prev => prev.filter(card => card.id !== cardId))
      toast.success('Card removido com sucesso!')
    } catch (error) {
      console.error('Erro ao remover card:', error)
      toast.error('Erro ao remover card')
    }
  }

  const handleShowTransactions = (transactions, title, total) => {
    setTransactionModalData({ transactions, title, total })
    setShowTransactionModal(true)
  }

  const handleDragStart = (card, e) => {
    e.preventDefault()
    const cardRect = e.currentTarget.closest('.dashboard-card').getBoundingClientRect()
    const containerRect = containerRef.current?.getBoundingClientRect()

    if (containerRect) {
      const offsetX = e.clientX - cardRect.left
      const offsetY = e.clientY - cardRect.top

      setDraggedCard(card)
      setDragOffset({ x: offsetX, y: offsetY })
      setIsDragging(true)
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 fade-in">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <LayoutDashboard className="h-8 w-8 text-purple-600" />
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          </div>

          {/* Seletor de Mês */}
          <div className="relative">
            <button
              onClick={() => setShowMonthDropdown(!showMonthDropdown)}
              className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-colors shadow-sm"
            >
              <span className="font-medium">{months[selectedMonth - 1]}</span>
              <ChevronDown className="ml-2 h-4 w-4" />
            </button>

            {showMonthDropdown && (
              <div className="absolute left-0 mt-2 w-40 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                {months.map((month, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedMonth(index + 1)
                      setShowMonthDropdown(false)
                    }}
                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                      index + 1 === selectedMonth ? 'bg-gray-50 font-medium' : ''
                    }`}
                  >
                    {month}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Controles */}
        <div className="flex items-center space-x-4">
          {/* Perfil Atual */}
          {currentProfile && (
            <div className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-4 py-2 rounded-lg shadow-sm">
              <span className="text-sm opacity-90">Perfil</span>
              <div className="text-lg font-bold">{currentProfile.name}</div>
            </div>
          )}

          <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-4 py-2 rounded-lg shadow-sm">
            <span className="text-sm opacity-90">Cards Ativos</span>
            <div className="text-lg font-bold">{cards.filter(card => card.visible).length}</div>
          </div>

          <button
            onClick={() => setShowProfileManager(true)}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg hover:from-indigo-700 hover:to-indigo-800 transition-colors shadow-sm"
          >
            <Settings className="h-4 w-4 mr-2" />
            Perfis
          </button>

          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-colors shadow-sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Adicionar Card
          </button>
        </div>
      </div>

      {/* Dashboard Grid */}
      <div
        ref={containerRef}
        className="relative bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6"
        style={{
          backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(139, 92, 246, 0.1) 1px, transparent 0)',
          backgroundSize: '20px 20px',
          minHeight: 'calc(100vh - 200px)',
          height: 'auto'
        }}
      >
        {cards.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-96 text-center">
            <Grid3X3 className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              Seu dashboard está vazio
            </h3>
            <p className="text-gray-500 mb-6 max-w-md">
              Comece adicionando cards personalizados para visualizar seus dados financeiros
              da forma que preferir.
            </p>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-colors shadow-lg"
            >
              <Plus className="h-5 w-5 mr-2" />
              Adicionar Primeiro Card
            </button>
          </div>
        ) : (
          <>
            {cards.filter(card => card.visible).map((card) => (
              <DashboardCard
                key={card.id}
                card={card}
                onUpdate={(updatedCard) => {
                  setCards(prev => prev.map(c => c.id === card.id ? updatedCard : c))
                }}
                onDelete={handleCardDelete}
                onShowTransactions={handleShowTransactions}
                isDragging={isDragging && draggedCard?.id === card.id}
                onDragStart={(e) => handleDragStart(card, e)}
                onDragEnd={() => {
                  setIsDragging(false)
                  setDraggedCard(null)
                }}
                className="dashboard-card"
                style={{
                  position: 'absolute',
                  left: card.position.x,
                  top: card.position.y,
                  width: card.position.w * 80 + (card.position.w - 1) * 20,
                  minHeight: card.position.h * 80,
                  zIndex: isDragging && draggedCard?.id === card.id ? 1000 : 1,
                  transition: isDragging && draggedCard?.id === card.id ? 'none' : 'all 0.2s ease-out'
                }}
              />
            ))}
          </>
        )}
      </div>

      {/* Modais */}
      <AddCardModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onCardAdded={handleCardAdded}
        profileId={currentProfile?.id}
      />

      <TransactionModal
        isOpen={showTransactionModal}
        onClose={() => setShowTransactionModal(false)}
        transactions={transactionModalData.transactions || []}
        title={transactionModalData.title || ''}
        total={transactionModalData.total || 0}
      />

      <ProfileManager
        isOpen={showProfileManager}
        onClose={() => setShowProfileManager(false)}
        currentProfile={currentProfile}
        onProfileChange={handleProfileChange}
      />
    </div>
  )
}

export default DashboardAnalytics
