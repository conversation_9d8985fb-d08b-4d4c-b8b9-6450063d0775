@echo off
echo ========================================
echo    🔧 Correção Rápida do Frontend
echo ========================================
echo.
echo Aplicando correções para tela branca...
echo.

echo 1. Parando serviços existentes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo.
echo 2. Limpando cache do npm...
cd frontend
call npm cache clean --force 2>nul

echo.
echo 3. Reinstalando dependências...
if exist "node_modules" (
    echo Removendo node_modules...
    rmdir /s /q node_modules 2>nul
)

if exist "package-lock.json" (
    echo Removendo package-lock.json...
    del package-lock.json 2>nul
)

echo Instalando dependências...
call npm install

echo.
echo 4. Verificando estrutura de arquivos...
if not exist "src\pages\LoansTest.jsx" (
    echo ❌ LoansTest.jsx não encontrado
    echo Criando arquivo básico...
    echo import React from 'react' > src\pages\LoansTest.jsx
    echo import Sidebar from '../components/Sidebar' >> src\pages\LoansTest.jsx
    echo. >> src\pages\LoansTest.jsx
    echo function LoansTest() { >> src\pages\LoansTest.jsx
    echo   return ( >> src\pages\LoansTest.jsx
    echo     ^<div className="flex h-screen bg-gray-50"^> >> src\pages\LoansTest.jsx
    echo       ^<Sidebar /^> >> src\pages\LoansTest.jsx
    echo       ^<div className="flex-1 p-8"^> >> src\pages\LoansTest.jsx
    echo         ^<h1^>Empréstimos - Teste^</h1^> >> src\pages\LoansTest.jsx
    echo       ^</div^> >> src\pages\LoansTest.jsx
    echo     ^</div^> >> src\pages\LoansTest.jsx
    echo   ) >> src\pages\LoansTest.jsx
    echo } >> src\pages\LoansTest.jsx
    echo. >> src\pages\LoansTest.jsx
    echo export default LoansTest >> src\pages\LoansTest.jsx
)

echo.
echo 5. Iniciando servidor de desenvolvimento...
echo ✅ Tudo pronto! Iniciando servidor...
echo.
echo 🌐 O navegador será aberto automaticamente
echo 📍 URL: http://localhost:5173/loans
echo.
echo ⚠️  Se ainda aparecer tela branca:
echo    1. Pressione Ctrl+Shift+R para recarregar
echo    2. Pressione F12 e veja o console
echo    3. Execute debug-frontend.bat
echo.

start http://localhost:5173/loans
call npm run dev

cd ..
