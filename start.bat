@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 🚀 Sistema Financeiro SARA - Auto Restart
echo ========================================
echo.

echo ⏳ Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nao encontrado!
    echo Execute install.bat primeiro
    pause
    exit /b 1
)

echo ✅ Iniciando sistema com monitoramento...
echo.
echo 🌐 O sistema sera aberto em:
echo    Frontend: http://localhost:5173
echo    Backend:  http://localhost:3001
echo.
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.
echo 🔄 Auto-restart ativado - O sistema será reiniciado automaticamente se cair
echo ⚠️  Para parar o sistema, feche esta janela ou pressione Ctrl+C
echo.

echo 🚀 Iniciando backend com auto-restart...
start "SARA Backend Monitor" cmd /k "cd /d %~dp0 && call monitor-backend.bat"

timeout /t 3 /nobreak >nul

echo 🚀 Iniciando frontend com auto-restart...
start "SARA Frontend Monitor" cmd /k "cd /d %~dp0 && call monitor-frontend.bat"

echo.
echo ✅ Sistema iniciado com monitoramento ativo!
echo 📊 Monitore o status nas janelas abertas
echo.
pause
