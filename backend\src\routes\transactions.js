const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const { upload, uploadToCloudinary } = require('../middleware/upload');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar transações
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, type, categoryId, startDate, endDate } = req.query;
    const userId = req.user.id;

    const where = { userId };

    if (type) where.type = type;
    if (categoryId) where.categoryId = categoryId;
    if (startDate || endDate) {
      where.date = {};
      if (startDate) where.date.gte = new Date(startDate);
      if (endDate) where.date.lte = new Date(endDate);
    }

    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        category: true,
        bank: true,
        paymentMethod: true
      },
      orderBy: { date: 'desc' },
      skip: (page - 1) * limit,
      take: parseInt(limit)
    });

    const total = await prisma.transaction.count({ where });

    res.json({
      transactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Erro ao buscar transações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar transação
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const { description, amount, type, categoryId, bankId, paymentMethodId, date } = req.body;
    const userId = req.user.id;

    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Descrição, valor e tipo são obrigatórios' });
    }

    if (!['INCOME', 'EXPENSE', 'INVESTMENT', 'LOAN'].includes(type)) {
      return res.status(400).json({ error: 'Tipo de transação inválido' });
    }

    // Upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const uploadResult = await uploadToCloudinary(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        receiptUrl = uploadResult.secure_url;
      } catch (uploadError) {
        console.error('Erro ao fazer upload do comprovante:', uploadError);
        // Continuar sem o comprovante se o upload falhar
      }
    }

    // Atualizar saldo do banco se especificado
    if (bankId) {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      // Atualizar saldo do banco
      const balanceChange = type === 'INCOME' ? parseFloat(amount) : -parseFloat(amount);
      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + balanceChange }
      });
    }

    // Atualizar fatura do cartão de crédito se especificado
    if (paymentMethodId && type === 'EXPENSE') {
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId, type: 'CREDIT' }
      });

      if (paymentMethod) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: paymentMethod.currentBill + parseFloat(amount),
            isBillPaid: false
          }
        });
      }
    }

    const transaction = await prisma.transaction.create({
      data: {
        description,
        amount: parseFloat(amount),
        type,
        categoryId: categoryId || null,
        bankId: bankId || null,
        paymentMethodId: paymentMethodId || null,
        receiptUrl: receiptUrl,
        date: date ? new Date(date) : new Date(),
        userId
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true
      }
    });

    res.status(201).json(transaction);
  } catch (error) {
    console.error('Erro ao criar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar transação
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { description, amount, type, categoryId, bankId, paymentMethodId, date } = req.body;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        description,
        amount: amount ? parseFloat(amount) : undefined,
        type,
        categoryId: categoryId !== undefined ? categoryId : undefined,
        bankId: bankId !== undefined ? bankId : undefined,
        paymentMethodId: paymentMethodId !== undefined ? paymentMethodId : undefined,
        date: date ? new Date(date) : undefined
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true
      }
    });

    res.json(transaction);
  } catch (error) {
    console.error('Erro ao atualizar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar transação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    await prisma.transaction.delete({
      where: { id }
    });

    res.json({ message: 'Transação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
