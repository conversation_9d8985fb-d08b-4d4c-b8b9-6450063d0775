const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const { upload, uploadToCloudinary } = require('../middleware/upload');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar transações
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;

    const transactions = await prisma.transaction.findMany({
      where: { userId },
      include: {
        category: true,
        bank: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    res.json(transactions);
  } catch (error) {
    console.error('Erro ao buscar transações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar transação
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const { description, amount, type, categoryId, bankId, paymentMethodId, date, installments } = req.body;
    const userId = req.user.id;

    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Descrição, valor e tipo são obrigatórios' });
    }

    if (!['INCOME', 'EXPENSE', 'INVESTMENT', 'LOAN'].includes(type)) {
      return res.status(400).json({ error: 'Tipo de transação inválido' });
    }

    // Upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const uploadResult = await uploadToCloudinary(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        receiptUrl = uploadResult.secure_url;
      } catch (uploadError) {
        console.error('Erro ao fazer upload do comprovante:', uploadError);
        // Continuar sem o comprovante se o upload falhar
      }
    }

    // Atualizar saldo do banco se especificado
    if (bankId) {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      // Atualizar saldo do banco
      const balanceChange = type === 'INCOME' ? parseFloat(amount) : -parseFloat(amount);
      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + balanceChange }
      });
    }

    // Atualizar fatura do cartão de crédito se especificado (apenas para transações não parceladas)
    const numInstallments = parseInt(installments) || 1;
    const isInstallment = numInstallments > 1 && paymentMethodId;

    if (paymentMethodId && type === 'EXPENSE' && !isInstallment) {
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId, type: 'CREDIT' }
      });

      if (paymentMethod) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: paymentMethod.currentBill + parseFloat(amount),
            isBillPaid: false
          }
        });
      }
    }

    if (isInstallment) {
      // Verificar se é cartão de crédito
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId, type: 'CREDIT' }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Parcelamento só é permitido para cartões de crédito' });
      }

      // Criar transação principal
      const parentTransaction = await prisma.transaction.create({
        data: {
          description: `${description} (${numInstallments}x)`,
          amount: parseFloat(amount),
          type,
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          receiptUrl: receiptUrl,
          installments: numInstallments,
          currentInstallment: 1,
          date: date ? new Date(date) : new Date(),
          userId
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true
        }
      });

      // Criar parcelas
      const installmentAmount = parseFloat(amount) / numInstallments;
      const installmentTransactions = [];

      for (let i = 1; i <= numInstallments; i++) {
        const installmentDate = new Date(date ? new Date(date) : new Date());
        installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

        const installmentTransaction = await prisma.transaction.create({
          data: {
            description: `${description} (${i}/${numInstallments})`,
            amount: installmentAmount,
            type,
            categoryId: categoryId || null,
            bankId: bankId || null,
            paymentMethodId: paymentMethodId || null,
            installments: numInstallments,
            currentInstallment: i,
            parentTransactionId: parentTransaction.id,
            date: installmentDate,
            userId
          }
        });

        installmentTransactions.push(installmentTransaction);

        // Adicionar à fatura do cartão apenas a primeira parcela
        if (i === 1 && type === 'EXPENSE') {
          await prisma.paymentMethod.update({
            where: { id: paymentMethodId },
            data: {
              currentBill: paymentMethod.currentBill + installmentAmount,
              isBillPaid: false
            }
          });
        }
      }

      res.status(201).json({
        ...parentTransaction,
        installmentTransactions
      });
    } else {
      // Transação normal (sem parcelamento)
      const transaction = await prisma.transaction.create({
        data: {
          description,
          amount: parseFloat(amount),
          type,
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          receiptUrl: receiptUrl,
          installments: 1,
          currentInstallment: 1,
          date: date ? new Date(date) : new Date(),
          userId
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true
        }
      });

      res.status(201).json(transaction);
    }
  } catch (error) {
    console.error('Erro ao criar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar transação
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { description, amount, type, categoryId, bankId, paymentMethodId, date } = req.body;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        description,
        amount: amount ? parseFloat(amount) : undefined,
        type,
        categoryId: categoryId !== undefined ? categoryId : undefined,
        bankId: bankId !== undefined ? bankId : undefined,
        paymentMethodId: paymentMethodId !== undefined ? paymentMethodId : undefined,
        date: date ? new Date(date) : undefined
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true
      }
    });

    res.json(transaction);
  } catch (error) {
    console.error('Erro ao atualizar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar detalhes das parcelas de uma transação
router.get('/:id/installments', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const parentTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!parentTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    // Buscar todas as parcelas relacionadas
    const installments = await prisma.transaction.findMany({
      where: {
        OR: [
          { parentTransactionId: id },
          { id: id }
        ],
        userId
      },
      orderBy: { currentInstallment: 'asc' },
      include: {
        category: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    res.json(installments);
  } catch (error) {
    console.error('Erro ao buscar parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar transação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    await prisma.transaction.delete({
      where: { id }
    });

    res.json({ message: 'Transação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
